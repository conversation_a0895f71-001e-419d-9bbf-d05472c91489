#!/usr/bin/env python3
"""
Demo Email Analysis with Sample Data
Uses LM Studio (Gemma3-4b) and PostgreSQL for HVAC email intelligence
"""

import requests
import psycopg2
import json
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
LM_STUDIO_URL = "http://*************:1234"
POSTGRES_CONFIG = {
    'host': '**************',
    'port': '5432',
    'database': 'hvacdb',
    'user': 'hvacdb',
    'password': 'blaeritipol'
}

# Sample emails for demonstration
SAMPLE_EMAILS = [
    {
        'id': 'demo_001',
        'sender': '<EMAIL>',
        'subject': 'Awaria klimatyzacji - pilne',
        'content': '''<PERSON><PERSON><PERSON> do<PERSON>,

<PERSON><PERSON> awarię klimatyzacji LG w naszym biurze przy ul. Puławskiej 123 w Warszawie. 
Urządzenie całkowicie przestało działać i wyświetla błąd E1. 
To bardzo pilne, bo mamy dziś ważne spotkanie z klientami.

Proszę o szybki kontakt.
Tel: 601-234-567

Pozdrawiam,
Anna Kowalska
Dyrektor Handlowy''',
        'type': 'customer_emails'
    },
    {
        'id': 'demo_002', 
        'sender': '<EMAIL>',
        'subject': 'Oferta na klimatyzację do domu',
        'content': '''Witam,

Szukam oferty na klimatyzację do domu jednorodzinnego w Piasecznie.
Dom ma 120m2, potrzebuję 3 jednostki wewnętrzne.
Interesuje mnie Daikin lub Mitsubishi.

Budżet do 25 000 zł.
Kiedy mogliby Państwo przyjechać na wycenę?

Telefon: 502-345-678
Adres: ul. Słoneczna 45, Piaseczno

Jan Nowak''',
        'type': 'customer_emails'
    },
    {
        'id': 'demo_003',
        'sender': '<EMAIL>',
        'subject': 'Transkrypcja - serwis u Kowalskiego',
        'content': '''Transkrypcja rozmowy z klientem:

Klient: Pan Kowalski, ul. Marszałkowska 89, Warszawa
Telefon: 123-456-789
Data serwisu: 2024-05-30

Wykonane prace:
- Przegląd klimatyzacji Panasonic CS-E12PKEA
- Wymiana filtrów
- Uzupełnienie czynnika chłodniczego R32
- Czyszczenie jednostki zewnętrznej

Problem: Słabe chłodzenie, hałas podczas pracy
Rozwiązanie: Wyczyszczono parownik, wymieniono filtry, uzupełniono czynnik

Koszt: 450 zł
Status: Zakończone pomyślnie

Następny przegląd: za 6 miesięcy''',
        'type': 'transcriptions'
    },
    {
        'id': 'demo_004',
        'sender': '<EMAIL>', 
        'subject': 'Rekuperator - konsultacja',
        'content': '''Dzień dobry,

Planujemy otwarcie restauracji w Konstancinie i potrzebujemy systemu wentylacji.
Lokal ma 200m2, kuchnia 50m2.

Czy mogliby Państwo doradzić jaki rekuperator wybrać?
Zależy nam na ciszy i efektywności energetycznej.

Kontakt: 
Marek Wiśniewski
Tel: 600-111-222
Email: <EMAIL>

Adres: ul. Warszawska 67, Konstancin-Jeziorna

Pozdrawiam''',
        'type': 'customer_emails'
    }
]

class EmailAnalyzer:
    def __init__(self):
        self.lm_studio_url = LM_STUDIO_URL
        self.postgres_conn = psycopg2.connect(**POSTGRES_CONFIG)
        
    def analyze_with_gemma3(self, email_content: str, email_type: str) -> dict:
        """Analyze email using Gemma3-4b"""
        
        if email_type == 'customer_emails':
            prompt = f"""
Przeanalizuj ten email od klienta HVAC i wyodrębnij kluczowe informacje:

TREŚĆ EMAILA:
{email_content}

Odpowiedz w formacie JSON:
{{
    "intent": "service_request|quote_request|complaint|inquiry|consultation",
    "sentiment": "positive|neutral|negative",
    "priority": "low|medium|high|urgent",
    "customer_info": {{
        "name": "imię i nazwisko klienta",
        "phone": "numer telefonu",
        "address": "adres",
        "company": "nazwa firmy jeśli podana"
    }},
    "equipment_info": {{
        "type": "klimatyzacja|pompa_ciepla|rekuperator|wentylacja|other",
        "brand": "marka urządzenia",
        "model": "model urządzenia",
        "problem": "opis problemu"
    }},
    "service_type": "installation|repair|maintenance|consultation|quote",
    "urgency_level": 1-5,
    "estimated_cost": null,
    "location": "miasto/lokalizacja",
    "action_items": ["lista działań do wykonania"],
    "confidence_score": 0.0-1.0
}}

Skup się na terminologii HVAC i kontekście polskim.
"""
        else:  # transcriptions
            prompt = f"""
Przeanalizuj tę transkrypcję serwisu HVAC:

TRANSKRYPCJA:
{email_content}

Odpowiedz w formacie JSON:
{{
    "intent": "service_report|customer_call|technical_discussion",
    "sentiment": "positive|neutral|negative",
    "priority": "low|medium|high|urgent",
    "customer_info": {{
        "name": "nazwa klienta",
        "phone": "telefon",
        "address": "adres serwisu"
    }},
    "equipment_info": {{
        "type": "klimatyzacja|pompa_ciepla|rekuperator|other",
        "brand": "marka",
        "model": "model",
        "problem": "opis problemu technicznego"
    }},
    "service_type": "installation|repair|maintenance|consultation",
    "urgency_level": 1-5,
    "estimated_cost": null,
    "location": "lokalizacja serwisu",
    "action_items": ["działania follow-up"],
    "confidence_score": 0.0-1.0
}}
"""

        try:
            response = requests.post(
                f"{self.lm_studio_url}/v1/chat/completions",
                json={
                    "model": "gemma-3-4b-it-qat",
                    "messages": [
                        {"role": "system", "content": "Jesteś ekspertem analizy biznesowej HVAC. Analizujesz emaile i transkrypcje aby wyodrębnić strukturalne informacje biznesowe."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.3,
                    "max_tokens": 1000
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # Extract JSON from response
                try:
                    json_start = content.find('{')
                    json_end = content.rfind('}') + 1
                    if json_start != -1 and json_end != -1:
                        json_str = content[json_start:json_end]
                        analysis = json.loads(json_str)
                        analysis['model_used'] = 'gemma3-4b'
                        analysis['raw_response'] = content
                        return analysis
                except json.JSONDecodeError:
                    logger.warning("Failed to parse JSON from AI response")
                    
                # Fallback
                return {
                    "intent": "unknown", "sentiment": "neutral", "priority": "medium",
                    "customer_info": {}, "equipment_info": {}, "service_type": "unknown",
                    "urgency_level": 3, "estimated_cost": None, "location": "",
                    "action_items": [], "confidence_score": 0.5, "model_used": "gemma3-4b",
                    "raw_response": content
                }
            else:
                logger.error(f"LM Studio API error: {response.status_code}")
                return self._create_fallback_analysis()
                
        except Exception as e:
            logger.error(f"Error analyzing with Gemma3: {e}")
            return self._create_fallback_analysis()
            
    def _create_fallback_analysis(self) -> dict:
        """Fallback analysis when AI fails"""
        return {
            "intent": "unknown", "sentiment": "neutral", "priority": "medium",
            "customer_info": {}, "equipment_info": {}, "service_type": "unknown",
            "urgency_level": 3, "estimated_cost": None, "location": "",
            "action_items": [], "confidence_score": 0.1, "model_used": "fallback"
        }
        
    def save_analysis(self, email_data: dict, analysis: dict):
        """Save analysis to PostgreSQL"""
        try:
            with self.postgres_conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO enhanced_email_analysis 
                    (email_id, sender, subject, content, timestamp, analysis_type,
                     intent, sentiment, priority, customer_info, equipment_info, 
                     action_items, service_type, urgency_level, estimated_cost, 
                     location, processed_at, model_used, confidence_score)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                """, (
                    email_data['id'], email_data['sender'], email_data['subject'],
                    email_data['content'], datetime.now(), email_data['type'],
                    analysis.get('intent'), analysis.get('sentiment'), analysis.get('priority'),
                    json.dumps(analysis.get('customer_info', {})), 
                    json.dumps(analysis.get('equipment_info', {})),
                    json.dumps(analysis.get('action_items', [])), 
                    analysis.get('service_type'), analysis.get('urgency_level'),
                    analysis.get('estimated_cost'), analysis.get('location'),
                    datetime.now(), analysis.get('model_used'), analysis.get('confidence_score')
                ))
                record_id = cur.fetchone()[0]
                logger.info(f"✅ Saved analysis with ID: {record_id}")
                
            self.postgres_conn.commit()
            return record_id
            
        except Exception as e:
            logger.error(f"❌ Error saving analysis: {e}")
            self.postgres_conn.rollback()
            return None
            
    def process_demo_emails(self):
        """Process sample emails for demonstration"""
        logger.info("🚀 Starting demo email analysis...")
        
        for email_data in SAMPLE_EMAILS:
            logger.info(f"📧 Processing: {email_data['subject']}")
            
            # Analyze with AI
            analysis = self.analyze_with_gemma3(email_data['content'], email_data['type'])
            
            # Save to database
            record_id = self.save_analysis(email_data, analysis)
            
            # Display results
            print(f"\n{'='*60}")
            print(f"📧 EMAIL: {email_data['subject']}")
            print(f"👤 FROM: {email_data['sender']}")
            print(f"🎯 INTENT: {analysis.get('intent', 'unknown')}")
            print(f"😊 SENTIMENT: {analysis.get('sentiment', 'neutral')}")
            print(f"⚡ PRIORITY: {analysis.get('priority', 'medium')}")
            print(f"🔧 SERVICE: {analysis.get('service_type', 'unknown')}")
            print(f"📍 LOCATION: {analysis.get('location', 'N/A')}")
            
            customer_info = analysis.get('customer_info', {})
            if customer_info.get('name'):
                print(f"👤 CUSTOMER: {customer_info.get('name')}")
            if customer_info.get('phone'):
                print(f"📞 PHONE: {customer_info.get('phone')}")
                
            equipment_info = analysis.get('equipment_info', {})
            if equipment_info.get('type'):
                print(f"🏠 EQUIPMENT: {equipment_info.get('type')}")
            if equipment_info.get('brand'):
                print(f"🏷️ BRAND: {equipment_info.get('brand')}")
                
            print(f"🎯 CONFIDENCE: {analysis.get('confidence_score', 0.5):.2f}")
            print(f"💾 SAVED AS ID: {record_id}")
            
        logger.info("✅ Demo email analysis completed!")
        
    def show_analysis_summary(self):
        """Show summary of analyzed emails"""
        try:
            with self.postgres_conn.cursor() as cur:
                # Get summary statistics
                cur.execute("""
                    SELECT 
                        COUNT(*) as total_emails,
                        COUNT(DISTINCT intent) as unique_intents,
                        COUNT(DISTINCT service_type) as unique_services,
                        AVG(confidence_score) as avg_confidence,
                        AVG(urgency_level) as avg_urgency
                    FROM enhanced_email_analysis
                    WHERE processed_at >= NOW() - INTERVAL '1 hour'
                """)
                stats = cur.fetchone()
                
                # Get intent breakdown
                cur.execute("""
                    SELECT intent, COUNT(*) as count
                    FROM enhanced_email_analysis 
                    WHERE processed_at >= NOW() - INTERVAL '1 hour'
                    GROUP BY intent
                    ORDER BY count DESC
                """)
                intents = cur.fetchall()
                
                # Get priority breakdown
                cur.execute("""
                    SELECT priority, COUNT(*) as count
                    FROM enhanced_email_analysis
                    WHERE processed_at >= NOW() - INTERVAL '1 hour'
                    GROUP BY priority
                    ORDER BY 
                        CASE priority 
                            WHEN 'urgent' THEN 1
                            WHEN 'high' THEN 2
                            WHEN 'medium' THEN 3
                            WHEN 'low' THEN 4
                            ELSE 5
                        END
                """)
                priorities = cur.fetchall()
                
                print(f"\n{'='*60}")
                print("📊 EMAIL ANALYSIS SUMMARY")
                print(f"{'='*60}")
                print(f"📧 Total Emails Processed: {stats[0]}")
                print(f"🎯 Unique Intents: {stats[1]}")
                print(f"🔧 Unique Services: {stats[2]}")
                print(f"🎯 Average Confidence: {stats[3]:.2f}")
                print(f"⚡ Average Urgency: {stats[4]:.1f}")
                
                print(f"\n📋 INTENT BREAKDOWN:")
                for intent, count in intents:
                    print(f"  {intent}: {count}")
                    
                print(f"\n⚡ PRIORITY BREAKDOWN:")
                for priority, count in priorities:
                    print(f"  {priority}: {count}")
                    
        except Exception as e:
            logger.error(f"Error generating summary: {e}")

def main():
    """Main demo function"""
    print("🚀 HVAC Email Analysis Demo")
    print("Using LM Studio (Gemma3-4b) + PostgreSQL")
    print("="*60)
    
    analyzer = EmailAnalyzer()
    
    # Process demo emails
    analyzer.process_demo_emails()
    
    # Show summary
    analyzer.show_analysis_summary()
    
    print(f"\n{'='*60}")
    print("🎉 Demo completed successfully!")
    print("💡 The system is ready for real email processing")
    print("📧 Configure email credentials to process real emails")
    print("="*60)

if __name__ == "__main__":
    main()