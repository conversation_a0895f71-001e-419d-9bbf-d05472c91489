#!/usr/bin/env python3
"""
Test script for email analysis setup
"""

import requests
import pymongo
import psycopg2
import json
from datetime import datetime

# Configuration
LM_STUDIO_URL = "http://*************:1234"
MONGODB_URL = "******************************************************"
POSTGRES_CONFIG = {
    'host': '**************',
    'port': '5432',
    'database': 'hvacdb',
    'user': 'hvacdb',
    'password': 'blaeritipol'
}

def test_lm_studio():
    """Test LM Studio connection"""
    print("🧠 Testing LM Studio connection...")
    try:
        response = requests.get(f"{LM_STUDIO_URL}/v1/models", timeout=10)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ LM Studio connected successfully!")
            print(f"📋 Available models: {json.dumps(models, indent=2)}")
            return True
        else:
            print(f"❌ LM Studio connection failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ LM Studio connection error: {e}")
        return False

def test_mongodb():
    """Test MongoDB connection"""
    print("\n🍃 Testing MongoDB connection...")
    try:
        client = pymongo.MongoClient(MONGODB_URL)
        # Test connection
        client.admin.command('ping')
        print("✅ MongoDB connected successfully!")
        
        # List databases
        dbs = client.list_database_names()
        print(f"📋 Available databases: {dbs}")
        
        # Test creating a collection
        db = client['hvac_email_analysis']
        collection = db['test_collection']
        test_doc = {
            'test': True,
            'timestamp': datetime.now(),
            'message': 'Connection test successful'
        }
        result = collection.insert_one(test_doc)
        print(f"✅ Test document inserted with ID: {result.inserted_id}")
        
        # Clean up test document
        collection.delete_one({'_id': result.inserted_id})
        print("🧹 Test document cleaned up")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ MongoDB connection error: {e}")
        return False

def test_postgresql():
    """Test PostgreSQL connection"""
    print("\n🐘 Testing PostgreSQL connection...")
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        with conn.cursor() as cur:
            # Test basic query
            cur.execute("SELECT version();")
            version = cur.fetchone()
            print(f"✅ PostgreSQL connected successfully!")
            print(f"📋 Version: {version[0]}")
            
            # Check if our table exists
            cur.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_name = 'enhanced_email_analysis'
            """)
            table_exists = cur.fetchone()[0]
            if table_exists:
                print("✅ enhanced_email_analysis table exists")
            else:
                print("❌ enhanced_email_analysis table not found")
                
            # Test insert
            cur.execute("""
                INSERT INTO enhanced_email_analysis 
                (email_id, sender, subject, content, analysis_type, intent, sentiment, priority)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (
                'test_001', '<EMAIL>', 'Test Email', 'Test content',
                'test', 'test', 'neutral', 'low'
            ))
            test_id = cur.fetchone()[0]
            print(f"✅ Test record inserted with ID: {test_id}")
            
            # Clean up test record
            cur.execute("DELETE FROM enhanced_email_analysis WHERE id = %s", (test_id,))
            print("🧹 Test record cleaned up")
            
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL connection error: {e}")
        return False

def test_ai_analysis():
    """Test AI analysis with LM Studio"""
    print("\n🤖 Testing AI analysis...")
    try:
        test_email = """
        Dzień dobry,
        
        Mam problem z klimatyzacją Daikin w biurze. Urządzenie nie chłodzi prawidłowo 
        i wydaje dziwne dźwięki. Czy mogliby Państwo przyjechać na serwis?
        
        Adres: ul. Marszałkowska 123, Warszawa
        Telefon: 123-456-789
        
        Pozdrawiam,
        Jan Kowalski
        """
        
        prompt = f"""
Analyze this HVAC customer email and extract key information:

EMAIL CONTENT:
{test_email}

Please provide a JSON response with the following structure:
{{
    "intent": "service_request|quote_request|complaint|inquiry|other",
    "sentiment": "positive|neutral|negative",
    "priority": "low|medium|high|urgent",
    "customer_info": {{
        "name": "extracted customer name",
        "phone": "extracted phone number",
        "address": "extracted address"
    }},
    "equipment_info": {{
        "type": "klimatyzacja|pompa_ciepla|rekuperator|other",
        "brand": "equipment brand if mentioned",
        "problem": "described problem or need"
    }},
    "service_type": "installation|repair|maintenance|consultation",
    "urgency_level": 1-5,
    "location": "extracted location/city",
    "confidence_score": 0.0-1.0
}}
"""

        response = requests.post(
            f"{LM_STUDIO_URL}/v1/chat/completions",
            json={
                "model": "gemma-2-2b-it",
                "messages": [
                    {"role": "system", "content": "You are an expert HVAC business analyst."},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 1000
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print("✅ AI analysis successful!")
            print(f"📋 Response: {content}")
            
            # Try to parse JSON
            try:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start != -1 and json_end != -1:
                    json_str = content[json_start:json_end]
                    analysis = json.loads(json_str)
                    print("✅ JSON parsing successful!")
                    print(f"📊 Extracted data: {json.dumps(analysis, indent=2)}")
                    return True
            except json.JSONDecodeError:
                print("⚠️ JSON parsing failed, but AI responded")
                return True
                
        else:
            print(f"❌ AI analysis failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ AI analysis error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Email Analysis Setup Tests\n")
    
    results = {
        'lm_studio': test_lm_studio(),
        'mongodb': test_mongodb(),
        'postgresql': test_postgresql(),
        'ai_analysis': test_ai_analysis()
    }
    
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name.upper()}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Email analysis system is ready!")
        print("🚀 You can now run the email analysis pipeline.")
    else:
        print("⚠️ Some tests failed. Please check the configuration.")
    print("="*50)

if __name__ == "__main__":
    main()