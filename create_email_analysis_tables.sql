-- Email Analysis Tables for HVAC CRM

-- Email analysis table
CREATE TABLE IF NOT EXISTS email_analysis (
    id BIGSERIAL PRIMARY KEY,
    email_id TEXT NOT NULL,
    sender TEXT,
    subject TEXT,
    content TEXT,
    timestamp TIMESTAMP WITH TIME ZONE,
    analysis_type TEXT, -- 'customer_emails' or 'transcriptions'
    
    -- AI Analysis Results
    intent TEXT, -- service_request, quote_request, complaint, inquiry, other
    sentiment TEXT, -- positive, neutral, negative
    priority TEXT, -- low, medium, high, urgent
    customer_info JSONB, -- extracted customer information
    equipment_info JSONB, -- equipment details
    action_items JSONB, -- list of required actions
    
    -- HVAC Specific
    service_type TEXT, -- installation, repair, maintenance, consultation
    urgency_level INTEGER DEFAULT 3, -- 1-5 scale
    estimated_cost DECIMAL(10, 2),
    location TEXT,
    
    -- Metadata
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    model_used TEXT DEFAULT 'gemma3-4b',
    confidence_score DECIMAL(3, 2) DEFAULT 0.5,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email attachments table (for transcription files)
CREATE TABLE IF NOT EXISTS email_attachments (
    id BIGSERIAL PRIMARY KEY,
    email_analysis_id BIGINT REFERENCES email_analysis(id) ON DELETE CASCADE,
    filename TEXT,
    file_type TEXT,
    file_size INTEGER,
    file_content BYTEA, -- Store small files directly
    file_path TEXT, -- Path to larger files on disk
    transcription_text TEXT, -- Extracted text from audio files
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customer insights derived from emails
CREATE TABLE IF NOT EXISTS customer_email_insights (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT REFERENCES customers(id),
    email_analysis_id BIGINT REFERENCES email_analysis(id),
    
    -- Derived insights
    communication_frequency INTEGER DEFAULT 0,
    avg_response_time INTERVAL,
    satisfaction_score DECIMAL(3, 2),
    preferred_contact_method TEXT,
    
    -- Equipment history from emails
    equipment_mentions JSONB,
    service_history JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI processing queue for batch processing
CREATE TABLE IF NOT EXISTS ai_processing_queue (
    id BIGSERIAL PRIMARY KEY,
    email_id TEXT NOT NULL,
    account_type TEXT,
    status TEXT DEFAULT 'pending', -- pending, processing, completed, failed
    priority INTEGER DEFAULT 5,
    retry_count INTEGER DEFAULT 0,
    error_message TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_email_analysis_sender ON email_analysis(sender);
CREATE INDEX IF NOT EXISTS idx_email_analysis_timestamp ON email_analysis(timestamp);
CREATE INDEX IF NOT EXISTS idx_email_analysis_intent ON email_analysis(intent);
CREATE INDEX IF NOT EXISTS idx_email_analysis_priority ON email_analysis(priority);
CREATE INDEX IF NOT EXISTS idx_email_analysis_service_type ON email_analysis(service_type);
CREATE INDEX IF NOT EXISTS idx_email_analysis_urgency ON email_analysis(urgency_level);
CREATE INDEX IF NOT EXISTS idx_email_analysis_processed_at ON email_analysis(processed_at);

-- GIN indexes for JSONB columns
CREATE INDEX IF NOT EXISTS idx_email_analysis_customer_info ON email_analysis USING GIN(customer_info);
CREATE INDEX IF NOT EXISTS idx_email_analysis_equipment_info ON email_analysis USING GIN(equipment_info);
CREATE INDEX IF NOT EXISTS idx_email_analysis_action_items ON email_analysis USING GIN(action_items);

-- Views for analytics
CREATE OR REPLACE VIEW email_analysis_summary AS
SELECT 
    DATE_TRUNC('day', processed_at) as analysis_date,
    analysis_type,
    intent,
    sentiment,
    priority,
    service_type,
    COUNT(*) as email_count,
    AVG(confidence_score) as avg_confidence,
    AVG(urgency_level) as avg_urgency
FROM email_analysis 
WHERE processed_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', processed_at), analysis_type, intent, sentiment, priority, service_type
ORDER BY analysis_date DESC;

-- View for high priority items
CREATE OR REPLACE VIEW urgent_email_actions AS
SELECT 
    id,
    sender,
    subject,
    intent,
    priority,
    urgency_level,
    service_type,
    location,
    action_items,
    processed_at
FROM email_analysis 
WHERE priority IN ('high', 'urgent') 
   OR urgency_level >= 4
ORDER BY urgency_level DESC, processed_at DESC;

-- Function to update customer insights
CREATE OR REPLACE FUNCTION update_customer_insights()
RETURNS TRIGGER AS $$
BEGIN
    -- Update or insert customer insights based on email analysis
    INSERT INTO customer_email_insights (
        customer_id, 
        email_analysis_id,
        equipment_mentions,
        service_history
    )
    SELECT 
        c.id,
        NEW.id,
        NEW.equipment_info,
        jsonb_build_object(
            'service_type', NEW.service_type,
            'timestamp', NEW.timestamp,
            'urgency', NEW.urgency_level
        )
    FROM customers c
    WHERE c.email = split_part(NEW.sender, '<', 1)
       OR c.phone = (NEW.customer_info->>'phone')
       OR c.name ILIKE '%' || (NEW.customer_info->>'name') || '%'
    ON CONFLICT (customer_id, email_analysis_id) DO UPDATE SET
        equipment_mentions = EXCLUDED.equipment_mentions,
        service_history = EXCLUDED.service_history,
        updated_at = NOW();
        
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update customer insights
CREATE TRIGGER trigger_update_customer_insights
    AFTER INSERT ON email_analysis
    FOR EACH ROW
    EXECUTE FUNCTION update_customer_insights();