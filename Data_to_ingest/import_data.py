#!/usr/bin/env python3
"""
Script to import CSV data into the HVAC CRM database.
"""

import os
import csv
import json
import uuid
import psycopg2
import psycopg2.extras
from datetime import datetime
import re
import logging
from typing import Dict, List, Any, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("data_import.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Database connection parameters
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_PORT = os.environ.get('DB_PORT', '2137')
DB_NAME = os.environ.get('DB_NAME', 'postgres')
DB_USER = os.environ.get('DB_USER', 'postgres')
DB_PASSWORD = os.environ.get('DB_PASSWORD', 'postgres')

# CSV file paths
CLIENTS_CSV = 'clients_export.csv'
CLIENTS_TEST_CSV = 'clients_test.csv'
KARTOTEKA_CSV = 'Kartoteka kontrahentów_extracted.csv'
CALENDAR_CSV = 'calendar_archive.csv'
INVOICES_CSV = 'Eksportowanie dokumentów.csv'

# Data directory
DATA_DIR = '/root/hvac-crm/Data_to_ingest'

def connect_to_db():
    """Connect to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        logger.info("Connected to the database successfully")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to the database: {e}")
        raise

def create_tables(conn):
    """Create necessary tables if they don't exist."""
    try:
        with conn.cursor() as cur:
            with open(os.path.join(DATA_DIR, 'create_tables.sql'), 'r') as f:
                sql = f.read()
                cur.execute(sql)
            conn.commit()
            logger.info("Tables created successfully")
    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        conn.rollback()
        raise

def clean_phone_number(phone: str) -> str:
    """Clean and format phone number."""
    if not phone:
        return None
    
    # Remove all non-digit characters
    phone = re.sub(r'\D', '', phone)
    
    # If it starts with country code, standardize it
    if phone.startswith('48') and len(phone) > 9:
        phone = phone[2:]
    
    # Format as XXX-XXX-XXX if it's 9 digits
    if len(phone) == 9:
        return f"{phone[:3]}-{phone[3:6]}-{phone[6:]}"
    
    return phone

def clean_nip(nip: str) -> str:
    """Clean and format NIP (tax ID)."""
    if not nip:
        return None
    
    # Remove all non-digit and non-letter characters
    nip = re.sub(r'[^\w]', '', nip)
    
    # Format as XXX-XXX-XX-XX if it's 10 digits
    if len(nip) == 10 and nip.isdigit():
        return f"{nip[:3]}-{nip[3:6]}-{nip[6:8]}-{nip[8:]}"
    
    return nip

def clean_postal_code(postal_code: str) -> str:
    """Clean and format postal code."""
    if not postal_code:
        return None
    
    # Remove all non-digit and non-letter characters
    postal_code = re.sub(r'[^\w]', '', postal_code)
    
    # Format as XX-XXX if it's 5 digits
    if len(postal_code) == 5 and postal_code.isdigit():
        return f"{postal_code[:2]}-{postal_code[2:]}"
    
    return postal_code

def import_clients(conn, filename: str):
    """Import client data from CSV file."""
    file_path = os.path.join(DATA_DIR, filename)
    
    if not os.path.exists(file_path):
        logger.warning(f"File not found: {file_path}")
        return
    
    try:
        with conn.cursor() as cur:
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                
                # Count total rows for progress reporting
                total_rows = sum(1 for _ in open(file_path, 'r', encoding='utf-8-sig')) - 1
                logger.info(f"Importing {total_rows} clients from {filename}")
                
                # Reset file pointer
                f.seek(0)
                next(reader)  # Skip header
                
                count = 0
                for row in reader:
                    # Skip empty rows
                    if not row.get('name'):
                        continue
                    
                    # Clean data
                    name = row.get('name', '').strip()
                    email = row.get('email', '').strip() if row.get('email') else None
                    phone = clean_phone_number(row.get('phone', ''))
                    nip = clean_nip(row.get('nip', ''))
                    
                    # Check if client already exists by name or NIP
                    cur.execute(
                        "SELECT id FROM customers WHERE name = %s OR (nip = %s AND nip IS NOT NULL)",
                        (name, nip)
                    )
                    existing = cur.fetchone()
                    
                    if existing:
                        customer_id = existing[0]
                        logger.info(f"Customer already exists: {name}, updating...")
                        
                        # Update existing customer
                        cur.execute(
                            """
                            UPDATE customers 
                            SET 
                                email = COALESCE(%s, email),
                                phone = COALESCE(%s, phone),
                                nip = COALESCE(%s, nip),
                                updated_at = NOW()
                            WHERE id = %s
                            """,
                            (email, phone, nip, customer_id)
                        )
                    else:
                        # Insert new customer
                        cur.execute(
                            """
                            INSERT INTO customers (name, email, phone, nip, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, NOW(), NOW())
                            RETURNING id
                            """,
                            (name, email, phone, nip)
                        )
                        customer_id = cur.fetchone()[0]
                    
                    # Insert address if available
                    street = row.get('street', '').strip() if row.get('street') else None
                    city = row.get('city', '').strip() if row.get('city') else None
                    postal_code = clean_postal_code(row.get('zip_code', ''))
                    address_type = row.get('address_type', 'Podstawowa').strip()
                    
                    if street or city or postal_code:
                        cur.execute(
                            """
                            INSERT INTO addresses 
                            (customer_id, street, city, postal_code, is_primary, address_type, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                            """,
                            (customer_id, street, city, postal_code, True, address_type)
                        )
                    
                    count += 1
                    if count % 100 == 0:
                        logger.info(f"Imported {count}/{total_rows} clients")
                        conn.commit()
            
            conn.commit()
            logger.info(f"Successfully imported {count} clients from {filename}")
    
    except Exception as e:
        logger.error(f"Error importing clients from {filename}: {e}")
        conn.rollback()
        raise

def import_calendar_events(conn, filename: str):
    """Import calendar events from CSV file."""
    file_path = os.path.join(DATA_DIR, filename)
    
    if not os.path.exists(file_path):
        logger.warning(f"File not found: {file_path}")
        return
    
    try:
        with conn.cursor() as cur:
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                
                # Count total rows for progress reporting
                total_rows = sum(1 for _ in open(file_path, 'r', encoding='utf-8-sig')) - 1
                logger.info(f"Importing {total_rows} calendar events from {filename}")
                
                # Reset file pointer
                f.seek(0)
                next(reader)  # Skip header
                
                count = 0
                for row in reader:
                    # Skip empty rows
                    if not row.get('Opis') or not row.get('Data'):
                        continue
                    
                    # Clean data
                    title = row.get('Opis', '').strip()
                    event_date = row.get('Data', '').strip()
                    category = row.get('Kategoria', '').strip()
                    customer = row.get('Klient', '').strip() if row.get('Klient') else None
                    address = row.get('Adres', '').strip() if row.get('Adres') else None
                    city = row.get('Miasto', '').strip() if row.get('Miasto') else None
                    phone = clean_phone_number(row.get('Telefon', ''))
                    device_type = row.get('Typ urządzenia', '').strip() if row.get('Typ urządzenia') else None
                    brand = row.get('Marka', '').strip() if row.get('Marka') else None
                    model = row.get('Model', '').strip() if row.get('Model') else None
                    quantity = row.get('Ilość', '1').strip()
                    notes = row.get('Notatki', '').strip() if row.get('Notatki') else None
                    
                    # Parse AI fields if available
                    ai_category = row.get('AI_Kategoria', '').strip() if row.get('AI_Kategoria') else None
                    ai_keywords = row.get('AI_SlowKluczowe', '').strip() if row.get('AI_SlowKluczowe') else None
                    ai_priority = row.get('AI_Priorytet', '').strip() if row.get('AI_Priorytet') else None
                    
                    # Try to parse the date
                    try:
                        start_time = datetime.fromisoformat(event_date.replace('Z', '+00:00'))
                        # Default end time is 2 hours after start
                        end_time = datetime.fromisoformat(event_date.replace('Z', '+00:00'))
                        end_time = end_time.replace(hour=end_time.hour + 2)
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid date format: {event_date}, skipping")
                        continue
                    
                    # Build description from available fields
                    description_parts = []
                    if address:
                        description_parts.append(f"Adres: {address}")
                    if city:
                        description_parts.append(f"Miasto: {city}")
                    if phone:
                        description_parts.append(f"Telefon: {phone}")
                    if device_type:
                        description_parts.append(f"Urządzenie: {device_type}")
                    if brand:
                        description_parts.append(f"Marka: {brand}")
                    if model:
                        description_parts.append(f"Model: {model}")
                    if quantity and quantity != '1':
                        description_parts.append(f"Ilość: {quantity}")
                    if notes:
                        description_parts.append(f"Notatki: {notes}")
                    if ai_keywords:
                        try:
                            ai_data = json.loads(ai_keywords)
                            for key, value in ai_data.items():
                                if value and value != "null":
                                    description_parts.append(f"{key}: {value}")
                        except json.JSONDecodeError:
                            description_parts.append(f"Słowa kluczowe: {ai_keywords}")
                    
                    description = "\n".join(description_parts)
                    
                    # Determine event type and status
                    event_type = ai_category if ai_category else category
                    status = "Zaplanowane"
                    if event_date < datetime.now().isoformat():
                        status = "Zakończone"
                    
                    # Set location
                    location = f"{address}, {city}" if address and city else (address or city or "")
                    
                    # Check if event already exists
                    cur.execute(
                        "SELECT id FROM calendar_events WHERE title = %s AND start_time = %s",
                        (title, start_time)
                    )
                    existing = cur.fetchone()
                    
                    if existing:
                        event_id = existing[0]
                        logger.info(f"Calendar event already exists: {title}, updating...")
                        
                        # Update existing event
                        cur.execute(
                            """
                            UPDATE calendar_events 
                            SET 
                                description = %s,
                                end_time = %s,
                                location = %s,
                                customer = %s,
                                device = %s,
                                type = %s,
                                status = %s,
                                updated_at = NOW()
                            WHERE id = %s
                            """,
                            (description, end_time, location, customer, device_type, event_type, status, event_id)
                        )
                    else:
                        # Insert new event
                        cur.execute(
                            """
                            INSERT INTO calendar_events 
                            (calendar_id, title, description, start_time, end_time, location, 
                             is_all_day, is_recurring, customer, device, type, status, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                            """,
                            ('default', title, description, start_time, end_time, location, 
                             False, False, customer, device_type, event_type, status)
                        )
                    
                    count += 1
                    if count % 100 == 0:
                        logger.info(f"Imported {count}/{total_rows} calendar events")
                        conn.commit()
            
            conn.commit()
            logger.info(f"Successfully imported {count} calendar events from {filename}")
    
    except Exception as e:
        logger.error(f"Error importing calendar events from {filename}: {e}")
        conn.rollback()
        raise

def import_invoices(conn, filename: str):
    """Import invoice data from CSV file."""
    file_path = os.path.join(DATA_DIR, filename)
    
    if not os.path.exists(file_path):
        logger.warning(f"File not found: {file_path}")
        return
    
    try:
        with conn.cursor() as cur:
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                # This file uses semicolons as separators
                reader = csv.DictReader(f, delimiter=';')
                
                # Count total rows for progress reporting
                total_rows = sum(1 for _ in open(file_path, 'r', encoding='utf-8-sig')) - 1
                logger.info(f"Importing {total_rows} invoices from {filename}")
                
                # Reset file pointer
                f.seek(0)
                next(reader)  # Skip header
                
                count = 0
                for row in reader:
                    # Skip empty rows
                    if not row.get('NUMER_DOK'):
                        continue
                    
                    # Clean data
                    invoice_number = row.get('NUMER_DOK', '').strip()
                    customer_name = row.get('NAZWA_PEL', '').strip()
                    nip = clean_nip(row.get('NIP', ''))
                    
                    # Try to parse dates
                    try:
                        issue_date = datetime.strptime(row.get('DATA_DOK', ''), '%Y-%m-%d')
                    except (ValueError, TypeError):
                        issue_date = None
                    
                    try:
                        due_date = datetime.strptime(row.get('TERMIN_P', ''), '%Y-%m-%d')
                    except (ValueError, TypeError):
                        due_date = None
                    
                    # Parse amounts
                    try:
                        total_net = float(row.get('NETTO', '0').replace(',', '.'))
                    except (ValueError, TypeError):
                        total_net = 0
                    
                    try:
                        total_vat = float(row.get('VAT', '0').replace(',', '.'))
                    except (ValueError, TypeError):
                        total_vat = 0
                    
                    try:
                        total_gross = float(row.get('BRUTTO', '0').replace(',', '.'))
                    except (ValueError, TypeError):
                        total_gross = 0
                    
                    payment_method = row.get('FORMA_P', '').strip()
                    
                    # Find customer by name or NIP
                    customer_id = None
                    if nip:
                        cur.execute("SELECT id FROM customers WHERE nip = %s", (nip,))
                        result = cur.fetchone()
                        if result:
                            customer_id = result[0]
                    
                    if not customer_id and customer_name:
                        cur.execute("SELECT id FROM customers WHERE name = %s", (customer_name,))
                        result = cur.fetchone()
                        if result:
                            customer_id = result[0]
                    
                    # If customer doesn't exist, create it
                    if not customer_id and customer_name:
                        cur.execute(
                            """
                            INSERT INTO customers (name, nip, created_at, updated_at)
                            VALUES (%s, %s, NOW(), NOW())
                            RETURNING id
                            """,
                            (customer_name, nip)
                        )
                        customer_id = cur.fetchone()[0]
                        
                        # Add address if available
                        address = row.get('ADRES', '').strip() if row.get('ADRES') else None
                        city = row.get('MIASTO', '').strip() if row.get('MIASTO') else None
                        postal_code = clean_postal_code(row.get('KOD', ''))
                        
                        if address or city or postal_code:
                            cur.execute(
                                """
                                INSERT INTO addresses 
                                (customer_id, street, city, postal_code, is_primary, created_at, updated_at)
                                VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                                """,
                                (customer_id, address, city, postal_code, True)
                            )
                    
                    # Check if invoice already exists
                    cur.execute(
                        "SELECT id FROM invoices WHERE invoice_number = %s",
                        (invoice_number,)
                    )
                    existing = cur.fetchone()
                    
                    if existing:
                        invoice_id = existing[0]
                        logger.info(f"Invoice already exists: {invoice_number}, updating...")
                        
                        # Update existing invoice
                        cur.execute(
                            """
                            UPDATE invoices 
                            SET 
                                customer_id = %s,
                                issue_date = %s,
                                due_date = %s,
                                total_net = %s,
                                total_vat = %s,
                                total_gross = %s,
                                payment_method = %s,
                                updated_at = NOW()
                            WHERE id = %s
                            """,
                            (customer_id, issue_date, due_date, total_net, total_vat, total_gross, payment_method, invoice_id)
                        )
                    else:
                        # Insert new invoice
                        cur.execute(
                            """
                            INSERT INTO invoices 
                            (invoice_number, customer_id, issue_date, due_date, total_net, total_vat, total_gross, 
                             status, payment_method, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                            """,
                            (invoice_number, customer_id, issue_date, due_date, total_net, total_vat, total_gross, 
                             'Wystawiona', payment_method)
                        )
                    
                    count += 1
                    if count % 100 == 0:
                        logger.info(f"Imported {count}/{total_rows} invoices")
                        conn.commit()
            
            conn.commit()
            logger.info(f"Successfully imported {count} invoices from {filename}")
    
    except Exception as e:
        logger.error(f"Error importing invoices from {filename}: {e}")
        conn.rollback()
        raise

def main():
    """Main function to run the import process."""
    logger.info("Starting data import process")
    
    try:
        # Connect to the database
        conn = connect_to_db()
        
        # Create tables if they don't exist
        create_tables(conn)
        
        # Import clients
        import_clients(conn, CLIENTS_CSV)
        import_clients(conn, CLIENTS_TEST_CSV)
        import_clients(conn, KARTOTEKA_CSV)
        
        # Import calendar events
        import_calendar_events(conn, CALENDAR_CSV)
        
        # Import invoices
        import_invoices(conn, INVOICES_CSV)
        
        # Close connection
        conn.close()
        logger.info("Data import process completed successfully")
    
    except Exception as e:
        logger.error(f"Error in data import process: {e}")
        raise

if __name__ == "__main__":
    main()
