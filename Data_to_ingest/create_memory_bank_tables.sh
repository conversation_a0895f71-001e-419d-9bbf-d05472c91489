#!/bin/bash

# Script to create Memory Bank tables in the HVAC CRM database

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Creating Memory Bank tables for HVAC CRM${NC}"

# Database connection parameters
DB_HOST=localhost
DB_PORT=5433
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=Blaeritipol1

# Check if psql is installed
if ! command -v psql &> /dev/null; then
    echo -e "${RED}Error: psql is not installed. Please install PostgreSQL client.${NC}"
    exit 1
fi

# Run the SQL script
echo -e "${YELLOW}Running SQL script to create Memory Bank tables...${NC}"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f /root/hvac-crm/Data_to_ingest/create_memory_bank_tables.sql

# Check if the script was executed successfully
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Memory Bank tables created successfully!${NC}"
    
    # Verify that the tables were created
    echo -e "${YELLOW}Verifying tables...${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE 'email_%' OR table_name LIKE 'document_%' OR table_name LIKE 'interaction_%' OR table_name = 'knowledge_articles' OR table_name = 'vector_embeddings' OR table_name LIKE 'user_%' OR table_name LIKE 'technician%' OR table_name LIKE 'inventory_%' OR table_name LIKE 'purchase_%';"
    
    echo -e "${GREEN}Memory Bank tables setup completed!${NC}"
    exit 0
else
    echo -e "${RED}Failed to create Memory Bank tables. Please check the error messages above.${NC}"
    exit 1
fi
