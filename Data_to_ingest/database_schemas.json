{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Database Schemas", "description": "JSON Schemas for database tables used in the HVAC CRM system", "definitions": {"customer": {"type": "object", "title": "Customer", "description": "Customer information", "properties": {"id": {"type": "integer", "description": "Unique identifier for the customer"}, "name": {"type": ["string", "null"], "description": "Customer's name or company name"}, "email": {"type": ["string", "null"], "description": "Customer's email address", "format": "email"}, "phone": {"type": ["string", "null"], "description": "Customer's phone number", "pattern": "^[0-9]{9}$"}, "created_at": {"type": "string", "description": "Timestamp when the customer was created", "format": "date-time"}}, "required": ["id", "created_at"], "additionalProperties": false, "examples": [{"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "phone": "*********", "created_at": "2025-04-01T12:00:00Z"}]}, "address": {"type": "object", "title": "Address", "description": "Customer address information", "properties": {"id": {"type": "integer", "description": "Unique identifier for the address"}, "customer_id": {"type": "integer", "description": "Reference to the customer"}, "street": {"type": ["string", "null"], "description": "Street name and number"}, "city": {"type": ["string", "null"], "description": "City name"}, "postal_code": {"type": ["string", "null"], "description": "Postal code"}, "country": {"type": ["string", "null"], "description": "Country name", "default": "Polska"}, "is_primary": {"type": "boolean", "description": "Whether this is the primary address for the customer", "default": true}, "created_at": {"type": "string", "description": "Timestamp when the address was created", "format": "date-time"}}, "required": ["id", "customer_id", "created_at"], "additionalProperties": false, "examples": [{"id": 1, "customer_id": 1, "street": "Marszałkowska 12", "city": "Warszawa", "postal_code": "00-001", "country": "Polska", "is_primary": true, "created_at": "2025-04-01T12:00:00Z"}]}, "calendar_event": {"type": "object", "title": "Calendar Event", "description": "Calendar event information", "properties": {"id": {"type": "integer", "description": "Unique identifier for the calendar event"}, "calendar_id": {"type": "string", "description": "Identifier for the calendar"}, "summary": {"type": "string", "description": "Summary or title of the event"}, "start_time": {"type": "string", "description": "Start time of the event", "format": "date-time"}, "end_time": {"type": "string", "description": "End time of the event", "format": "date-time"}, "all_day": {"type": "boolean", "description": "Whether the event is an all-day event", "default": false}, "status": {"type": "string", "description": "Status of the event", "enum": ["Zaplanowane", "<PERSON> trakcie", "Zakończone", "<PERSON><PERSON><PERSON><PERSON>"]}, "customer_id": {"type": ["integer", "null"], "description": "Reference to the customer"}, "created_at": {"type": "string", "description": "Timestamp when the event was created", "format": "date-time"}}, "required": ["id", "summary", "start_time", "created_at"], "additionalProperties": false, "examples": [{"id": 1, "calendar_id": "default_calendar_id", "summary": "Montaż klimatyzacji u Kowalskiego", "start_time": "2025-04-01T10:00:00Z", "end_time": "2025-04-01T12:00:00Z", "all_day": false, "status": "Zaplanowane", "customer_id": 1, "created_at": "2025-03-25T12:00:00Z"}]}, "service_order": {"type": "object", "title": "Service Order", "description": "Service order information", "properties": {"id": {"type": "integer", "description": "Unique identifier for the service order"}, "customer_id": {"type": ["integer", "null"], "description": "Reference to the customer"}, "service_type": {"type": "string", "description": "Type of service", "enum": ["<PERSON><PERSON><PERSON>", "Montaż", "Oględziny", "<PERSON><PERSON><PERSON><PERSON>"]}, "status": {"type": "string", "description": "Status of the service order", "enum": ["Nowe", "Zaplanowane", "<PERSON> trakcie", "Zakończone", "<PERSON><PERSON><PERSON><PERSON>"]}, "scheduled_start": {"type": ["string", "null"], "description": "Scheduled start time", "format": "date-time"}, "cost": {"type": "number", "description": "Cost of the service", "minimum": 0}, "created_at": {"type": "string", "description": "Timestamp when the service order was created", "format": "date-time"}}, "required": ["id", "service_type", "status", "created_at"], "additionalProperties": false, "examples": [{"id": 1, "customer_id": 1, "service_type": "Montaż", "status": "Zaplanowane", "scheduled_start": "2025-04-01T10:00:00Z", "cost": 1500.0, "created_at": "2025-03-25T12:00:00Z"}]}, "device": {"type": "object", "title": "<PERSON><PERSON>", "description": "Device information", "properties": {"id": {"type": "integer", "description": "Unique identifier for the device"}, "customer_id": {"type": ["integer", "null"], "description": "Reference to the customer"}, "device_type": {"type": ["string", "null"], "description": "Type of the device", "enum": ["klimatyzacja", "pompa cie<PERSON>ła", "rekuperator", "ogrzewanie podłogowe", "kocioł", null]}, "brand": {"type": ["string", "null"], "description": "Brand of the device"}, "model": {"type": ["string", "null"], "description": "Model of the device"}, "serial_number": {"type": ["string", "null"], "description": "Serial number of the device"}, "installation_date": {"type": ["string", "null"], "description": "Date when the device was installed", "format": "date-time"}, "last_service_date": {"type": ["string", "null"], "description": "Date when the device was last serviced", "format": "date-time"}, "warranty_end_date": {"type": ["string", "null"], "description": "Date when the warranty ends", "format": "date-time"}, "notes": {"type": ["string", "null"], "description": "Additional notes about the device"}, "created_at": {"type": "string", "description": "Timestamp when the device was created", "format": "date-time"}}, "required": ["id", "created_at"], "additionalProperties": false, "examples": [{"id": 1, "customer_id": 1, "device_type": "klimatyzacja", "brand": "<PERSON>kin", "model": "FTXM35N", "serial_number": "ABC123456", "installation_date": "2025-04-01T12:00:00Z", "last_service_date": null, "warranty_end_date": "2027-04-01T12:00:00Z", "notes": "Zamontowany w salonie", "created_at": "2025-04-01T12:00:00Z"}]}, "service_device": {"type": "object", "title": "Service Device", "description": "Junction table linking service orders and devices", "properties": {"id": {"type": "integer", "description": "Unique identifier for the service device relationship"}, "service_order_id": {"type": "integer", "description": "Reference to the service order"}, "device_id": {"type": "integer", "description": "Reference to the device"}, "action_type": {"type": "string", "description": "Type of action performed on the device", "enum": ["installation", "service", "repair", "inspection", "removal"]}, "notes": {"type": ["string", "null"], "description": "Additional notes about the service performed on the device"}, "created_at": {"type": "string", "description": "Timestamp when the record was created", "format": "date-time"}}, "required": ["id", "service_order_id", "device_id", "action_type", "created_at"], "additionalProperties": false, "examples": [{"id": 1, "service_order_id": 1, "device_id": 1, "action_type": "installation", "notes": "Instalacja nowego urządzenia", "created_at": "2025-04-01T12:00:00Z"}]}}, "oneOf": [{"$ref": "#/definitions/customer"}, {"$ref": "#/definitions/address"}, {"$ref": "#/definitions/calendar_event"}, {"$ref": "#/definitions/service_order"}, {"$ref": "#/definitions/device"}, {"$ref": "#/definitions/service_device"}], "examples": [{"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "phone": "*********", "created_at": "2025-04-01T12:00:00Z"}, {"id": 1, "customer_id": 1, "device_type": "klimatyzacja", "brand": "<PERSON>kin", "model": "FTXM35N", "serial_number": "ABC123456", "installation_date": "2025-04-01T12:00:00Z", "last_service_date": null, "warranty_end_date": "2027-04-01T12:00:00Z", "notes": "Zamontowany w salonie", "created_at": "2025-04-01T12:00:00Z"}]}