#!/usr/bin/env python3
"""
Test database connection script.
"""

import psycopg2
import os

# Database connection parameters
DB_HOST = '**************'
DB_PORT = '5432'
DB_NAME = 'hvacdb'
DB_USER = 'hvacdb'
DB_PASSWORD = 'blaeritipol'

def test_connection():
    """Test the database connection."""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        print("✅ Database connection successful!")
        
        # Test a simple query
        with conn.cursor() as cur:
            cur.execute("SELECT version();")
            version = cur.fetchone()
            print(f"📊 PostgreSQL version: {version[0]}")
            
            # Check if tables exist
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """)
            tables = cur.fetchall()
            print(f"📋 Existing tables: {[table[0] for table in tables]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    test_connection()