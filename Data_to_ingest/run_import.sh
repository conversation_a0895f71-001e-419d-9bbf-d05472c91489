#!/bin/bash

# Script to run the data import process

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting HVAC CRM data import process${NC}"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Error: Python 3 is not installed. Please install Python 3.${NC}"
    exit 1
fi

# Check if psycopg2 is installed
python3 -c "import psycopg2" &> /dev/null
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}Installing psycopg2...${NC}"
    pip install psycopg2-binary
    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: Failed to install psycopg2. Please install it manually.${NC}"
        exit 1
    fi
fi

# Set environment variables from .env file
if [ -f "/root/hvac-crm/.env" ]; then
    echo -e "${GREEN}Loading environment variables from .env file${NC}"
    export $(grep -v '^#' /root/hvac-crm/.env | xargs)
fi

# Set database connection parameters
export DB_HOST=${DB_HOST:-localhost}
export DB_PORT=5433
export DB_NAME=${DB_NAME:-postgres}
export DB_USER=${DB_USER:-postgres}
export DB_PASSWORD=Blaeritipol1

echo -e "${GREEN}Using database connection:${NC}"
echo "Host: $DB_HOST"
echo "Port: $DB_PORT"
echo "Database: $DB_NAME"
echo "User: $DB_USER"

# Make the import script executable
chmod +x /root/hvac-crm/Data_to_ingest/import_data.py

# Run the import script
echo -e "${YELLOW}Running data import script...${NC}"
cd /root/hvac-crm/Data_to_ingest
python3 import_data.py

# Check if the import was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Data import completed successfully!${NC}"
    exit 0
else
    echo -e "${RED}Data import failed. Please check the logs.${NC}"
    exit 1
fi
