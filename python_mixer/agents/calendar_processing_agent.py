#!/usr/bin/env python3
"""
Calendar Processing Agent for HVAC CRM
Advanced NLP agent for extracting structured data from calendar event descriptions.

Features:
- Polish language support for HVAC terminology
- Equipment model recognition (LG, Daikin, etc.)
- Address extraction and standardization
- Phone number validation and formatting
- Service type classification
- Customer name extraction
- Confidence scoring for all extractions
"""

import re
import json
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass
from loguru import logger
import asyncio

try:
    from ..database.sql.advanced_customer_profile_models import (
        CalendarEvent, Customer, CustomerContact, CustomerAddress,
        CustomerInteraction, CustomerEquipment, ServiceType, InteractionType
    )
    from ..database.sql.models import db_manager
except ImportError:
    logger.warning("Database models not available - running in standalone mode")


@dataclass
class ExtractedCalendarData:
    """Structured data extracted from calendar event."""
    customer_name: Optional[str] = None
    phone_number: Optional[str] = None
    address: Optional[str] = None
    service_type: Optional[str] = None
    equipment_models: List[str] = None
    confidence_score: float = 0.0
    extraction_details: Dict[str, Any] = None

    def __post_init__(self):
        if self.equipment_models is None:
            self.equipment_models = []
        if self.extraction_details is None:
            self.extraction_details = {}


class CalendarProcessingAgent:
    """
    Advanced agent for processing calendar events and extracting HVAC-related data.
    
    Capabilities:
    - Polish text processing with HVAC domain knowledge
    - Multi-pattern extraction with confidence scoring
    - Equipment model recognition and validation
    - Address parsing and standardization
    - Customer matching and deduplication
    """

    def __init__(self):
        self.polish_names_patterns = self._load_polish_name_patterns()
        self.equipment_patterns = self._load_equipment_patterns()
        self.service_type_patterns = self._load_service_type_patterns()
        self.address_patterns = self._load_address_patterns()
        self.phone_patterns = self._load_phone_patterns()
        
        logger.info("Calendar Processing Agent initialized with Polish HVAC patterns")

    def _load_polish_name_patterns(self) -> List[re.Pattern]:
        """Load patterns for Polish names recognition."""
        patterns = [
            # Common Polish name patterns
            r'\b([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż]+)\s+([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż]+)\b',
            # Names with titles
            r'\b(?:Pan|Pani|Dr|Mgr|Inż)\.\s*([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż]+)\s+([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż]+)\b',
            # Names in various contexts
            r'(?:klient|właściciel|kontakt):\s*([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż]+)\s+([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż]+)',
        ]
        return [re.compile(pattern, re.IGNORECASE) for pattern in patterns]

    def _load_equipment_patterns(self) -> Dict[str, List[re.Pattern]]:
        """Load equipment model recognition patterns."""
        patterns = {
            'lg': [
                re.compile(r'\bLG\s+([A-Z0-9]+(?:ET|EV|ER|ES|EQ))\b', re.IGNORECASE),
                re.compile(r'\b(S\d{2}ET|S\d{2}EV|P\d{2}RT)\b', re.IGNORECASE),
                re.compile(r'\bDual\s+Cool\s+([A-Z0-9]+)\b', re.IGNORECASE),
            ],
            'daikin': [
                re.compile(r'\bDaikin\s+([A-Z0-9-]+)\b', re.IGNORECASE),
                re.compile(r'\b(FTXS\d{2}[A-Z]|FTXM\d{2}[A-Z]|RXS\d{2}[A-Z])\b', re.IGNORECASE),
                re.compile(r'\bSensira\s+([A-Z0-9]+)\b', re.IGNORECASE),
            ],
            'mitsubishi': [
                re.compile(r'\bMitsubishi\s+([A-Z0-9-]+)\b', re.IGNORECASE),
                re.compile(r'\b(MSZ-[A-Z0-9]+|MUZ-[A-Z0-9]+)\b', re.IGNORECASE),
            ],
            'generic': [
                re.compile(r'\b(\d{1,2}(?:\.\d)?)\s*kW\b', re.IGNORECASE),
                re.compile(r'\b(\d{4,5})\s*BTU\b', re.IGNORECASE),
                re.compile(r'\bklimatyzacja\s+([a-ząćęłńóśźż\s]+)', re.IGNORECASE),
            ]
        }
        return patterns

    def _load_service_type_patterns(self) -> Dict[str, List[re.Pattern]]:
        """Load service type classification patterns."""
        patterns = {
            'installation': [
                re.compile(r'\b(?:montaż|instalacja|założenie|podłączenie)\b', re.IGNORECASE),
                re.compile(r'\bnowy\s+(?:montaż|system)\b', re.IGNORECASE),
            ],
            'repair': [
                re.compile(r'\b(?:naprawa|awaria|usterka|nie\s+działa|zepsuty)\b', re.IGNORECASE),
                re.compile(r'\b(?:błąd|error|kod\s+błędu)\s*[A-Z0-9]*\b', re.IGNORECASE),
                re.compile(r'\b(?:nie\s+chłodzi|nie\s+grzeje|przeciek)\b', re.IGNORECASE),
            ],
            'maintenance': [
                re.compile(r'\b(?:serwis|przegląd|konserwacja|czyszczenie)\b', re.IGNORECASE),
                re.compile(r'\b(?:okresowy|planowy|rutynowy)\s+(?:serwis|przegląd)\b', re.IGNORECASE),
            ],
            'inspection': [
                re.compile(r'\b(?:kontrola|inspekcja|sprawdzenie|diagnoza)\b', re.IGNORECASE),
                re.compile(r'\bwizja\s+lokalna\b', re.IGNORECASE),
            ],
            'consultation': [
                re.compile(r'\b(?:konsultacja|doradztwo|wycena|oferta)\b', re.IGNORECASE),
                re.compile(r'\b(?:spotkanie|prezentacja|omówienie)\b', re.IGNORECASE),
            ],
            'emergency': [
                re.compile(r'\b(?:pilne|nagłe|awaryjna|SOS|emergency)\b', re.IGNORECASE),
                re.compile(r'\b(?:natychmiast|dzisiaj|teraz)\b', re.IGNORECASE),
            ]
        }
        return patterns

    def _load_address_patterns(self) -> List[re.Pattern]:
        """Load Polish address recognition patterns."""
        patterns = [
            # Standard Polish addresses
            r'\b(?:ul\.|ulica|al\.|aleja|os\.|osiedle|pl\.|plac)\s+([^,\n]+?)(?:\s+\d+[a-z]?(?:/\d+)?)',
            # Address with postal code
            r'\b(\d{2}-\d{3})\s+([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż\s]+)',
            # City names
            r'\b(Warszawa|Kraków|Gdańsk|Wrocław|Poznań|Łódź|Katowice|Lublin|Białystok|Szczecin|Gdynia|Częstochowa|Radom|Sosnowiec|Toruń|Kielce|Gliwice|Zabrze|Bytom|Bielsko-Biała|Olsztyn|Rzeszów|Ruda Śląska|Rybnik|Tychy|Dąbrowa Górnicza|Płock|Elbląg|Opole|Gorzów Wielkopolski|Włocławek|Zielona Góra|Tarnów|Chorzów|Kalisz|Koszalin|Legnica|Grudziądz|Słupsk|Jaworzno|Jastrzębie-Zdrój|Nowy Sącz|Jelenia Góra|Siedlce|Mysłowice|Konin|Piła|Inowrocław|Lubin|Ostrowiec Świętokrzyski|Gniezno|Stargard|Pabianice|Leszno|Łomża|Żory|Pruszków|Zamość|Tomaszów Mazowiecki|Przemyśl|Stalowa Wola|Mielec|Chełm|Będzin|Zgierz|Kutno|Piotrków Trybunalski|Racibórz|Ostrów Wielkopolski|Świętochłowice|Starachowice|Zawiercie|Wałbrzych|Tarnowskie Góry|Puławy|Wodzisław Śląski|Ełk|Suwałki|Skierniewice|Radomsko|Kędzierzyn-Koźle|Legionowo|Ciechanów|Ostrołęka|Świdnica|Bolesławiec|Żary|Biała Podlaska|Otwock|Sieradz|Chrzanów|Żywiec|Sopot|Wejherowo|Siemianowice Śląskie|Sanok|Krosno|Brzeg|Wieluń|Luboń|Kraśnik|Giżycko|Augustów|Koło|Malbork|Nakło nad Notecią|Dzierżoniów|Oleśnica|Namysłów|Kłodzko|Prudnik|Nysa|Kluczbork|Środa Wielkopolska|Września|Wągrowiec|Złotów|Chojnice|Człuchów|Miastko|Słupsk|Lębork|Reda|Rumia|Pruszcz Gdański|Starogard Gdański|Tczew|Kwidzyn|Iława|Bartoszyce|Mrągowo|Pisz|Szczytno|Nidzica|Działdowo|Brodnica|Rypin|Lipno|Włocławek|Aleksandrów Kujawski|Kruszwica|Mogilno|Strzelno|Żnin|Inowrocław|Bydgoszcz|Nakło nad Notecią|Szubin|Żnin|Kcynia|Janowiec Wielkopolski|Rogoźno|Oborniki|Murowana Goślina|Pobiedziska|Kostrzyn|Mosina|Kórnik|Środa Wielkopolska|Śrem|Jarocin|Pleszew|Kalisz|Ostrów Wielkopolski|Ostrzeszów|Kępno|Syców|Oleśnica|Trzebnica|Żmigród|Milicz|Góra|Polkowice|Lubin|Głogów|Zielona Góra|Żagań|Żary|Gubinów|Krosno Odrzańskie|Nowa Sól|Świebodzin|Sulechów|Czerwieńsk|Bytom Odrzański|Sława|Szprotawa|Przemków|Chocianów|Bolesławiec|Złotoryja|Jawor|Legnica|Chojnów|Prochowice|Środa Śląska|Wołów|Brzeg Dolny|Oława|Strzelin|Ząbkowice Śląskie|Kłodzko|Bystrzyca Kłodzka|Lądek-Zdrój|Polanica-Zdrój|Kudowa-Zdrój|Nowa Ruda|Wałbrzych|Kamienna Góra|Lubawka|Piechowice|Szklarska Poręba|Karpacz|Kowary|Jelenia Góra|Zgorzelec|Bogatynia|Pieńsk|Węgliniec|Lubań|Świeradów-Zdrój|Mirsk|Gryfów Śląski|Lwówek Śląski|Złotoryja|Chojnów|Legnica|Prochowice|Środa Śląska|Wołów|Brzeg Dolny|Oława|Strzelin|Ząbkowice Śląskie|Kłodzko|Bystrzyca Kłodzka|Lądek-Zdrój|Polanica-Zdrój|Kudowa-Zdrój|Nowa Ruda|Wałbrzych|Kamienna Góra|Lubawka|Piechowice|Szklarska Poręba|Karpacz|Kowary|Jelenia Góra)\b',
            # Generic address patterns
            r'\b([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż\s]+)\s+\d+[a-z]?(?:/\d+)?\b',
        ]
        return [re.compile(pattern, re.IGNORECASE) for pattern in patterns]

    def _load_phone_patterns(self) -> List[re.Pattern]:
        """Load Polish phone number patterns."""
        patterns = [
            # Polish mobile numbers
            r'\b(?:\+48\s*)?(?:\d{3}\s*\d{3}\s*\d{3}|\d{9})\b',
            # With various separators
            r'\b(?:\+48[-\s]?)?(?:\d{3}[-\s]?\d{3}[-\s]?\d{3})\b',
            # Landline numbers
            r'\b(?:\+48\s*)?(?:\d{2}\s*\d{3}\s*\d{2}\s*\d{2}|\d{2}\s*\d{7})\b',
        ]
        return [re.compile(pattern) for pattern in patterns]

    async def process_calendar_event(self, event_data: Dict[str, Any]) -> ExtractedCalendarData:
        """
        Process a single calendar event and extract structured data.
        
        Args:
            event_data: Dictionary containing event information
            
        Returns:
            ExtractedCalendarData with extracted information and confidence scores
        """
        try:
            title = event_data.get('title', '')
            description = event_data.get('description', '')
            location = event_data.get('location', '')
            
            # Combine all text for analysis
            full_text = f"{title} {description} {location}".strip()
            
            if not full_text:
                return ExtractedCalendarData(confidence_score=0.0)
            
            logger.debug(f"Processing calendar event: {title[:50]}...")
            
            # Extract different components
            customer_name = self._extract_customer_name(full_text)
            phone_number = self._extract_phone_number(full_text)
            address = self._extract_address(full_text)
            service_type = self._extract_service_type(full_text)
            equipment_models = self._extract_equipment_models(full_text)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score({
                'customer_name': customer_name,
                'phone_number': phone_number,
                'address': address,
                'service_type': service_type,
                'equipment_models': equipment_models
            })
            
            extraction_details = {
                'text_length': len(full_text),
                'extraction_method': 'regex_nlp',
                'processed_at': datetime.now().isoformat(),
                'patterns_matched': self._get_matched_patterns(full_text)
            }
            
            result = ExtractedCalendarData(
                customer_name=customer_name,
                phone_number=phone_number,
                address=address,
                service_type=service_type,
                equipment_models=equipment_models,
                confidence_score=confidence_score,
                extraction_details=extraction_details
            )
            
            logger.info(f"Calendar event processed - Confidence: {confidence_score:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing calendar event: {e}")
            return ExtractedCalendarData(
                confidence_score=0.0,
                extraction_details={'error': str(e)}
            )

    def _extract_customer_name(self, text: str) -> Optional[str]:
        """Extract customer name from text using Polish name patterns."""
        for pattern in self.polish_names_patterns:
            matches = pattern.findall(text)
            if matches:
                # Return the first match, combining first and last name
                if isinstance(matches[0], tuple):
                    return f"{matches[0][0]} {matches[0][1]}"
                else:
                    return matches[0]
        return None

    def _extract_phone_number(self, text: str) -> Optional[str]:
        """Extract and validate Polish phone number."""
        for pattern in self.phone_patterns:
            matches = pattern.findall(text)
            if matches:
                phone = matches[0]
                # Standardize phone number format
                phone = re.sub(r'[-\s]', '', phone)
                if not phone.startswith('+48'):
                    if len(phone) == 9:
                        phone = f'+48{phone}'
                    elif len(phone) == 11 and phone.startswith('48'):
                        phone = f'+{phone}'
                return phone
        return None

    def _extract_address(self, text: str) -> Optional[str]:
        """Extract address from text using Polish address patterns."""
        for pattern in self.address_patterns:
            matches = pattern.findall(text)
            if matches:
                # Return the most complete address found
                address = matches[0]
                if isinstance(address, tuple):
                    address = ' '.join(filter(None, address))
                return address.strip()
        return None

    def _extract_service_type(self, text: str) -> Optional[str]:
        """Classify service type based on text content."""
        service_scores = {}
        
        for service_type, patterns in self.service_type_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(pattern.findall(text))
                score += matches
            if score > 0:
                service_scores[service_type] = score
        
        if service_scores:
            # Return service type with highest score
            return max(service_scores, key=service_scores.get)
        return None

    def _extract_equipment_models(self, text: str) -> List[str]:
        """Extract equipment models and brands from text."""
        equipment_found = []
        
        for brand, patterns in self.equipment_patterns.items():
            for pattern in patterns:
                matches = pattern.findall(text)
                for match in matches:
                    if isinstance(match, tuple):
                        equipment_found.extend(match)
                    else:
                        equipment_found.append(match)
        
        # Remove duplicates and empty strings
        return list(filter(None, set(equipment_found)))

    def _calculate_confidence_score(self, extracted_data: Dict[str, Any]) -> float:
        """Calculate confidence score based on extracted data quality."""
        score = 0.0
        max_score = 5.0
        
        # Customer name (weight: 1.0)
        if extracted_data.get('customer_name'):
            score += 1.0
        
        # Phone number (weight: 1.5)
        if extracted_data.get('phone_number'):
            score += 1.5
        
        # Address (weight: 1.0)
        if extracted_data.get('address'):
            score += 1.0
        
        # Service type (weight: 0.8)
        if extracted_data.get('service_type'):
            score += 0.8
        
        # Equipment models (weight: 0.7)
        if extracted_data.get('equipment_models'):
            score += 0.7
        
        return min(score / max_score, 1.0)

    def _get_matched_patterns(self, text: str) -> List[str]:
        """Get list of pattern types that matched in the text."""
        matched = []
        
        # Check name patterns
        for pattern in self.polish_names_patterns:
            if pattern.search(text):
                matched.append('customer_name')
                break
        
        # Check phone patterns
        for pattern in self.phone_patterns:
            if pattern.search(text):
                matched.append('phone_number')
                break
        
        # Check address patterns
        for pattern in self.address_patterns:
            if pattern.search(text):
                matched.append('address')
                break
        
        # Check service type patterns
        for service_type, patterns in self.service_type_patterns.items():
            for pattern in patterns:
                if pattern.search(text):
                    matched.append(f'service_type_{service_type}')
                    break
        
        # Check equipment patterns
        for brand, patterns in self.equipment_patterns.items():
            for pattern in patterns:
                if pattern.search(text):
                    matched.append(f'equipment_{brand}')
                    break
        
        return matched
