#!/usr/bin/env python3
"""
Unified Customer System Launcher
Complete HVAC CRM system with advanced customer profile management.

Features:
- Enhanced Human Comprehension Interface
- Unified Customer Management
- Calendar Processing Agent
- Email Analysis Integration
- Data Consistency Engine
- Multi-interface deployment
"""

import asyncio
import sys
import time
from pathlib import Path
from typing import Optional
import subprocess
import threading
from loguru import logger

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> - <level>{message}</level>",
    level="INFO"
)
logger.add(
    "logs/unified_customer_system.log",
    rotation="10 MB",
    retention="7 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name} - {message}",
    level="DEBUG"
)


class UnifiedCustomerSystemLauncher:
    """
    Comprehensive launcher for the unified HVAC customer management system.
    
    Manages multiple interfaces and services:
    - Enhanced Human Comprehension Interface (port 7862)
    - Unified Customer Management Interface (port 7863)
    - System health monitoring
    - Database initialization
    - Service coordination
    """

    def __init__(self):
        self.services = {}
        self.is_running = False
        self.startup_time = time.time()
        
        logger.info("🌟 Unified Customer System Launcher initialized")

    def validate_system(self) -> bool:
        """Validate system requirements and dependencies."""
        logger.info("🔍 Validating system requirements...")
        
        try:
            # Check Python version
            if sys.version_info < (3, 8):
                logger.error("❌ Python 3.8+ required")
                return False
            
            # Check required packages
            required_packages = [
                "gradio", "loguru", "plotly", "pandas", "numpy", 
                "sqlalchemy", "asyncio", "re", "json"
            ]
            
            missing_packages = []
            for package in required_packages:
                try:
                    __import__(package)
                    logger.debug(f"✅ {package} available")
                except ImportError:
                    missing_packages.append(package)
            
            if missing_packages:
                logger.error(f"❌ Missing packages: {', '.join(missing_packages)}")
                logger.info("💡 Install with: pip install " + " ".join(missing_packages))
                return False
            
            # Check directories
            required_dirs = ["logs", "data", "temp", "database", "agents", "engines", "interfaces"]
            for dir_name in required_dirs:
                dir_path = Path(dir_name)
                if not dir_path.exists():
                    dir_path.mkdir(parents=True, exist_ok=True)
                    logger.info(f"📁 Created directory: {dir_path}")
            
            logger.info("✅ System validation completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ System validation failed: {e}")
            return False

    def initialize_database(self) -> bool:
        """Initialize database with advanced customer profile models."""
        logger.info("🗄️ Initializing database...")
        
        try:
            from database.sql.advanced_customer_profile_models import create_all_tables
            from database.sql.models import db_manager
            
            # Create database engine
            engine = db_manager.get_engine()
            
            if engine:
                # Create all tables
                create_all_tables(engine)
                logger.info("✅ Database tables created successfully")
                
                # Test connection
                if db_manager.is_connected():
                    logger.info("✅ Database connection verified")
                    return True
                else:
                    logger.warning("⚠️ Database not connected - running in demo mode")
                    return True  # Allow demo mode
            else:
                logger.warning("⚠️ Database engine not available - running in demo mode")
                return True  # Allow demo mode
                
        except Exception as e:
            logger.warning(f"⚠️ Database initialization failed: {e} - running in demo mode")
            return True  # Allow demo mode

    def start_enhanced_interface(self) -> bool:
        """Start Enhanced Human Comprehension Interface."""
        logger.info("🎨 Starting Enhanced Human Comprehension Interface...")
        
        try:
            from enhanced_human_comprehension_interface import launch_enhanced_interface
            
            # Start in separate thread
            def run_interface():
                try:
                    launch_enhanced_interface()
                except Exception as e:
                    logger.error(f"❌ Enhanced interface error: {e}")
            
            interface_thread = threading.Thread(target=run_interface, daemon=True)
            interface_thread.start()
            
            self.services['enhanced_interface'] = {
                'thread': interface_thread,
                'port': 7862,
                'status': 'running',
                'start_time': time.time()
            }
            
            logger.info("✅ Enhanced Human Comprehension Interface started on port 7862")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start Enhanced Interface: {e}")
            return False

    def start_customer_interface(self) -> bool:
        """Start Unified Customer Management Interface."""
        logger.info("🏢 Starting Unified Customer Management Interface...")
        
        try:
            from interfaces.unified_customer_interface import UnifiedCustomerInterface
            import gradio as gr
            
            def run_customer_interface():
                try:
                    customer_interface = UnifiedCustomerInterface()
                    interface = customer_interface.create_interface()
                    
                    interface.launch(
                        server_name="0.0.0.0",
                        server_port=7863,
                        share=False,
                        debug=True,
                        show_error=True
                    )
                except Exception as e:
                    logger.error(f"❌ Customer interface error: {e}")
            
            customer_thread = threading.Thread(target=run_customer_interface, daemon=True)
            customer_thread.start()
            
            self.services['customer_interface'] = {
                'thread': customer_thread,
                'port': 7863,
                'status': 'running',
                'start_time': time.time()
            }
            
            logger.info("✅ Unified Customer Management Interface started on port 7863")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start Customer Interface: {e}")
            return False

    def start_calendar_agent(self) -> bool:
        """Initialize Calendar Processing Agent."""
        logger.info("📅 Initializing Calendar Processing Agent...")
        
        try:
            from agents.calendar_processing_agent import CalendarProcessingAgent
            
            calendar_agent = CalendarProcessingAgent()
            
            self.services['calendar_agent'] = {
                'agent': calendar_agent,
                'status': 'ready',
                'start_time': time.time()
            }
            
            logger.info("✅ Calendar Processing Agent initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Calendar Agent: {e}")
            return False

    def start_unification_engine(self) -> bool:
        """Initialize Customer Unification Engine."""
        logger.info("🔗 Initializing Customer Unification Engine...")
        
        try:
            from engines.customer_unification_engine import CustomerUnificationEngine
            
            unification_engine = CustomerUnificationEngine()
            
            self.services['unification_engine'] = {
                'engine': unification_engine,
                'status': 'ready',
                'start_time': time.time()
            }
            
            logger.info("✅ Customer Unification Engine initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Unification Engine: {e}")
            return False

    def monitor_services(self):
        """Monitor service health and status."""
        while self.is_running:
            try:
                uptime = time.time() - self.startup_time
                
                # Log service status
                active_services = len([s for s in self.services.values() if s.get('status') == 'running'])
                logger.debug(f"💓 System health - Uptime: {uptime:.1f}s, Active services: {active_services}")
                
                # Check service threads
                for service_name, service_info in self.services.items():
                    if 'thread' in service_info:
                        if not service_info['thread'].is_alive():
                            logger.warning(f"⚠️ Service {service_name} thread stopped")
                            service_info['status'] = 'stopped'
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"❌ Service monitoring error: {e}")
                time.sleep(5)

    def launch_system(self) -> bool:
        """Launch the complete unified customer system."""
        logger.info("🚀 Launching Unified Customer Management System...")
        
        # System validation
        if not self.validate_system():
            logger.error("❌ System validation failed")
            return False
        
        # Database initialization
        if not self.initialize_database():
            logger.error("❌ Database initialization failed")
            return False
        
        # Start core services
        services_to_start = [
            ("Calendar Agent", self.start_calendar_agent),
            ("Unification Engine", self.start_unification_engine),
            ("Enhanced Interface", self.start_enhanced_interface),
            ("Customer Interface", self.start_customer_interface),
        ]
        
        failed_services = []
        for service_name, start_func in services_to_start:
            if not start_func():
                failed_services.append(service_name)
        
        if failed_services:
            logger.warning(f"⚠️ Some services failed to start: {', '.join(failed_services)}")
        
        # Start monitoring
        self.is_running = True
        monitor_thread = threading.Thread(target=self.monitor_services, daemon=True)
        monitor_thread.start()
        
        # Display system status
        self.display_system_status()
        
        logger.info("🎉 Unified Customer Management System launched successfully!")
        return True

    def display_system_status(self):
        """Display comprehensive system status."""
        logger.info("=" * 80)
        logger.info("🌟 UNIFIED CUSTOMER MANAGEMENT SYSTEM - STATUS")
        logger.info("=" * 80)
        
        logger.info("🎯 INTERFACES:")
        logger.info("   • Enhanced Human Comprehension: http://localhost:7862")
        logger.info("   • Unified Customer Management:  http://localhost:7863")
        
        logger.info("🔧 SERVICES:")
        for service_name, service_info in self.services.items():
            status_icon = "✅" if service_info.get('status') == 'running' else "🟡"
            port_info = f" (port {service_info.get('port')})" if service_info.get('port') else ""
            logger.info(f"   {status_icon} {service_name.replace('_', ' ').title()}{port_info}")
        
        logger.info("📊 FEATURES:")
        logger.info("   • Advanced customer profile management")
        logger.info("   • Email analysis integration")
        logger.info("   • Calendar event processing")
        logger.info("   • Data consistency engine")
        logger.info("   • Duplicate detection and merging")
        logger.info("   • Human comprehension optimization")
        
        logger.info("=" * 80)

    def shutdown(self):
        """Graceful system shutdown."""
        logger.info("🛑 Shutting down Unified Customer Management System...")
        
        self.is_running = False
        
        # Stop services
        for service_name, service_info in self.services.items():
            if 'thread' in service_info and service_info['thread'].is_alive():
                logger.info(f"🛑 Stopping {service_name}...")
                # Gradio doesn't have direct shutdown, threads will be daemon
        
        logger.info("✅ System shutdown completed")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Unified Customer Management System Launcher"
    )
    parser.add_argument(
        "--demo", 
        action="store_true",
        help="Run in demo mode without database"
    )
    
    args = parser.parse_args()
    
    try:
        launcher = UnifiedCustomerSystemLauncher()
        
        if launcher.launch_system():
            logger.info("🎉 System running successfully!")
            logger.info("Press Ctrl+C to shutdown...")
            
            # Keep main thread alive
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                launcher.shutdown()
        else:
            logger.error("❌ Failed to launch system")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ System error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
