#!/usr/bin/env python3
"""
Unified Customer Interface for HVAC CRM
Advanced interface for managing comprehensive customer profiles with all interactions.

Features:
- Complete customer timeline view
- Email, calendar, and transcription integration
- Equipment registry management
- Data quality monitoring
- Duplicate detection and merging
- Interactive customer search and filtering
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

import gradio as gr
from loguru import logger

try:
    from ..database.sql.advanced_customer_profile_models import (
        Customer, CustomerContact, CustomerAddress, CustomerInteraction,
        CustomerEquipment, CalendarEvent, get_customer_unified_profile,
        CustomerStatus, InteractionType, ServiceType
    )
    from ..database.sql.models import db_manager
    from ..engines.customer_unification_engine import CustomerUnificationEngine
    from ..agents.calendar_processing_agent import CalendarProcessingAgent
except ImportError:
    logger.warning("Database models not available - running in demo mode")


class UnifiedCustomerInterface:
    """
    Advanced interface for managing unified customer profiles.
    
    Provides comprehensive view of customer data with:
    - Complete interaction timeline
    - Equipment registry
    - Data quality metrics
    - Duplicate management
    - Search and filtering capabilities
    """

    def __init__(self):
        self.unification_engine = CustomerUnificationEngine()
        self.calendar_agent = CalendarProcessingAgent()
        
        # Interface state
        self.current_customer_id = None
        self.search_results = []
        self.duplicate_candidates = []
        
        logger.info("Unified Customer Interface initialized")

    def create_interface(self) -> gr.Blocks:
        """Create the unified customer management interface."""
        
        # Enhanced CSS for customer management
        customer_css = """
        /* Customer Profile Styling */
        .customer-profile-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #e3f2fd;
            border-radius: 16px;
            padding: 24px;
            margin: 16px 0;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        }
        
        .customer-header {
            background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .interaction-timeline {
            background: #f8f9fa;
            border-left: 4px solid #1a73e8;
            padding: 16px;
            margin: 8px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .data-quality-high {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid #4caf50;
            color: #1b5e20;
        }
        
        .data-quality-medium {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            border: 2px solid #ff9800;
            color: #e65100;
        }
        
        .data-quality-low {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border: 2px solid #f44336;
            color: #c62828;
        }
        
        .equipment-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 12px;
            padding: 16px;
            margin: 8px 0;
        }
        
        .duplicate-warning {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
            border: 2px solid #ff9800;
            color: #e65100;
            padding: 16px;
            border-radius: 12px;
            margin: 16px 0;
        }
        """

        with gr.Blocks(
            title="🏢 Unified Customer Management",
            theme=gr.themes.Soft(
                primary_hue="blue",
                secondary_hue="green",
                neutral_hue="slate"
            ),
            css=customer_css
        ) as interface:

            # Header
            gr.HTML("""
            <div class="customer-header">
                <h1 style="margin: 0; font-size: 2rem; font-weight: 700;">
                    🏢 Unified Customer Management System
                </h1>
                <p style="margin: 8px 0 0 0; font-size: 1.1rem; opacity: 0.9;">
                    Complete customer profiles with email, calendar, and transcription integration
                </p>
            </div>
            """)

            with gr.Tabs():
                
                # Tab 1: Customer Search and Overview
                with gr.Tab("🔍 Customer Search"):
                    with gr.Row():
                        with gr.Column(scale=2):
                            gr.Markdown("### 🔍 Customer Search")
                            
                            search_input = gr.Textbox(
                                label="Search Customers",
                                placeholder="Enter name, email, phone, or address...",
                                lines=1
                            )
                            
                            with gr.Row():
                                search_btn = gr.Button("🔍 Search", variant="primary")
                                clear_search_btn = gr.Button("🗑️ Clear", variant="secondary")
                            
                            # Advanced filters
                            with gr.Accordion("🔧 Advanced Filters", open=False):
                                with gr.Row():
                                    status_filter = gr.Dropdown(
                                        choices=["All", "Prospect", "Active", "Inactive", "VIP"],
                                        value="All",
                                        label="Customer Status"
                                    )
                                    quality_filter = gr.Dropdown(
                                        choices=["All", "High (>0.8)", "Medium (0.5-0.8)", "Low (<0.5)"],
                                        value="All",
                                        label="Data Quality"
                                    )
                                
                                date_range = gr.DateRange(
                                    label="Last Interaction Date Range"
                                )
                        
                        with gr.Column(scale=3):
                            search_results_html = gr.HTML(
                                value=self._get_empty_search_results(),
                                label="Search Results"
                            )

                # Tab 2: Customer Profile View
                with gr.Tab("👤 Customer Profile"):
                    with gr.Row():
                        customer_selector = gr.Dropdown(
                            choices=[],
                            label="Select Customer",
                            interactive=True
                        )
                        refresh_profile_btn = gr.Button("🔄 Refresh", variant="secondary")
                    
                    # Customer profile display
                    customer_profile_html = gr.HTML(
                        value=self._get_empty_customer_profile(),
                        label="Customer Profile"
                    )
                    
                    with gr.Tabs():
                        # Interaction Timeline
                        with gr.Tab("📅 Interaction Timeline"):
                            interaction_timeline = gr.HTML(
                                value="Select a customer to view interaction timeline...",
                                label="Timeline"
                            )
                        
                        # Equipment Registry
                        with gr.Tab("🔧 Equipment Registry"):
                            equipment_registry = gr.HTML(
                                value="Select a customer to view equipment...",
                                label="Equipment"
                            )
                        
                        # Data Quality Analysis
                        with gr.Tab("📊 Data Quality"):
                            data_quality_plot = gr.Plot(
                                label="Data Quality Metrics"
                            )
                            
                            data_quality_details = gr.HTML(
                                value="Select a customer to view data quality analysis...",
                                label="Quality Details"
                            )

                # Tab 3: Calendar Processing
                with gr.Tab("📅 Calendar Processing"):
                    gr.Markdown("### 📅 Calendar Event Processing")
                    
                    with gr.Row():
                        with gr.Column():
                            calendar_input = gr.Textbox(
                                label="Calendar Event Description",
                                placeholder="Paste calendar event description here...\n\nExample:\nMontaż klimatyzacji LG S12ET\nJan Kowalski - 123 456 789\nul. Testowa 123, Warszawa\n10:00-12:00",
                                lines=8
                            )
                            
                            process_calendar_btn = gr.Button(
                                "🔄 Process Calendar Event", 
                                variant="primary"
                            )
                        
                        with gr.Column():
                            calendar_results = gr.JSON(
                                label="Extracted Information",
                                value={}
                            )
                            
                            calendar_customer_match = gr.HTML(
                                value="Process a calendar event to see customer matching...",
                                label="Customer Matching"
                            )

                # Tab 4: Duplicate Management
                with gr.Tab("🔄 Duplicate Management"):
                    gr.Markdown("### 🔄 Duplicate Detection and Management")
                    
                    with gr.Row():
                        detect_duplicates_btn = gr.Button(
                            "🔍 Detect Duplicates", 
                            variant="primary"
                        )
                        duplicate_threshold = gr.Slider(
                            minimum=0.5,
                            maximum=1.0,
                            value=0.8,
                            step=0.05,
                            label="Similarity Threshold"
                        )
                    
                    duplicate_results = gr.HTML(
                        value="Click 'Detect Duplicates' to find potential duplicate customers...",
                        label="Duplicate Candidates"
                    )

                # Tab 5: Data Integration
                with gr.Tab("🔗 Data Integration"):
                    gr.Markdown("### 🔗 Email and Transcription Integration")
                    
                    with gr.Row():
                        with gr.Column():
                            integration_source = gr.Radio(
                                choices=["Email Analysis", "Transcription Data"],
                                value="Email Analysis",
                                label="Data Source"
                            )
                            
                            integration_data = gr.Textbox(
                                label="Data to Integrate",
                                placeholder="Paste email content or transcription data...",
                                lines=10
                            )
                            
                            integrate_btn = gr.Button(
                                "🔗 Integrate Data", 
                                variant="primary"
                            )
                        
                        with gr.Column():
                            integration_results = gr.HTML(
                                value="Integration results will appear here...",
                                label="Integration Results"
                            )

            # Event handlers
            search_btn.click(
                fn=self._search_customers,
                inputs=[search_input, status_filter, quality_filter, date_range],
                outputs=[search_results_html, customer_selector]
            )

            clear_search_btn.click(
                fn=lambda: ("", self._get_empty_search_results(), []),
                outputs=[search_input, search_results_html, customer_selector]
            )

            customer_selector.change(
                fn=self._load_customer_profile,
                inputs=[customer_selector],
                outputs=[
                    customer_profile_html, 
                    interaction_timeline, 
                    equipment_registry,
                    data_quality_plot,
                    data_quality_details
                ]
            )

            process_calendar_btn.click(
                fn=self._process_calendar_event,
                inputs=[calendar_input],
                outputs=[calendar_results, calendar_customer_match]
            )

            detect_duplicates_btn.click(
                fn=self._detect_duplicates,
                inputs=[duplicate_threshold],
                outputs=[duplicate_results]
            )

            integrate_btn.click(
                fn=self._integrate_data,
                inputs=[integration_source, integration_data],
                outputs=[integration_results]
            )

        return interface

    def _get_empty_search_results(self) -> str:
        """Get empty search results HTML."""
        return """
        <div class="customer-profile-card">
            <h4 style="margin: 0 0 12px 0; color: #666;">🔍 Customer Search Results</h4>
            <p style="margin: 0; color: #888;">Enter search criteria and click 'Search' to find customers.</p>
            <div style="margin-top: 16px; padding: 12px; background: #f0f0f0; border-radius: 8px; text-align: center;">
                <strong>💡 Search Tips:</strong><br>
                • Use partial names, emails, or phone numbers<br>
                • Filter by status and data quality<br>
                • Set date ranges for recent interactions
            </div>
        </div>
        """

    def _get_empty_customer_profile(self) -> str:
        """Get empty customer profile HTML."""
        return """
        <div class="customer-profile-card">
            <h4 style="margin: 0 0 12px 0; color: #666;">👤 Customer Profile</h4>
            <p style="margin: 0; color: #888;">Select a customer from the dropdown to view their complete profile.</p>
            <div style="margin-top: 16px; padding: 12px; background: #f0f0f0; border-radius: 8px;">
                <strong>📋 Profile includes:</strong><br>
                • Complete contact information<br>
                • Interaction timeline (emails, calls, visits)<br>
                • Equipment registry with service history<br>
                • Data quality analysis and scoring
            </div>
        </div>
        """

    async def _search_customers(
        self,
        search_query: str,
        status_filter: str,
        quality_filter: str,
        date_range: tuple
    ) -> tuple:
        """Search customers based on criteria."""
        try:
            if not search_query.strip():
                return self._get_empty_search_results(), []

            logger.info(f"Searching customers: {search_query}")

            # Simulate database search (replace with actual implementation)
            mock_results = [
                {
                    'id': 1,
                    'name': 'Jan Kowalski',
                    'email': '<EMAIL>',
                    'phone': '+48123456789',
                    'status': 'Active',
                    'quality_score': 0.85,
                    'last_interaction': '2024-01-15',
                    'interaction_count': 12,
                    'equipment_count': 2
                },
                {
                    'id': 2,
                    'name': 'Anna Nowak',
                    'email': '<EMAIL>',
                    'phone': '+48987654321',
                    'status': 'Prospect',
                    'quality_score': 0.65,
                    'last_interaction': '2024-01-10',
                    'interaction_count': 3,
                    'equipment_count': 0
                }
            ]

            # Filter results based on search query
            filtered_results = [
                r for r in mock_results
                if search_query.lower() in r['name'].lower() or
                   search_query.lower() in r['email'].lower() or
                   search_query in r['phone']
            ]

            # Generate results HTML
            results_html = self._generate_search_results_html(filtered_results)

            # Generate dropdown choices
            dropdown_choices = [
                f"{r['id']}: {r['name']} ({r['email']})"
                for r in filtered_results
            ]

            return results_html, dropdown_choices

        except Exception as e:
            logger.error(f"Error searching customers: {e}")
            return f"<div class='data-quality-low'>Error: {str(e)}</div>", []

    def _generate_search_results_html(self, results: List[Dict]) -> str:
        """Generate HTML for search results."""
        if not results:
            return """
            <div class="customer-profile-card">
                <h4 style="margin: 0 0 12px 0; color: #666;">🔍 No Results Found</h4>
                <p style="margin: 0; color: #888;">No customers match your search criteria.</p>
            </div>
            """

        html = f"""
        <div class="customer-profile-card">
            <h4 style="margin: 0 0 16px 0; color: #1a73e8;">🔍 Found {len(results)} Customer(s)</h4>
        """

        for result in results:
            quality_class = self._get_quality_class(result['quality_score'])

            html += f"""
            <div class="customer-profile-card" style="margin: 12px 0; border-left: 4px solid #1a73e8;">
                <div style="display: flex; justify-content: space-between; align-items: start;">
                    <div>
                        <h5 style="margin: 0 0 8px 0; color: #1a73e8;">
                            👤 {result['name']}
                        </h5>
                        <p style="margin: 0 0 4px 0; color: #666;">
                            📧 {result['email']} • 📞 {result['phone']}
                        </p>
                        <p style="margin: 0; color: #888; font-size: 0.9em;">
                            📅 Last contact: {result['last_interaction']} •
                            💬 {result['interaction_count']} interactions •
                            🔧 {result['equipment_count']} equipment
                        </p>
                    </div>
                    <div style="text-align: right;">
                        <span class="{quality_class}" style="padding: 4px 8px; border-radius: 12px; font-size: 0.8em; font-weight: 600;">
                            {result['status']}
                        </span>
                        <div style="margin-top: 4px; font-size: 0.8em; color: #666;">
                            Quality: {result['quality_score']:.1%}
                        </div>
                    </div>
                </div>
            </div>
            """

        html += "</div>"
        return html

    def _get_quality_class(self, score: float) -> str:
        """Get CSS class based on quality score."""
        if score >= 0.8:
            return "data-quality-high"
        elif score >= 0.5:
            return "data-quality-medium"
        else:
            return "data-quality-low"

    async def _load_customer_profile(self, customer_selection: str) -> tuple:
        """Load complete customer profile."""
        try:
            if not customer_selection:
                return (
                    self._get_empty_customer_profile(),
                    "Select a customer to view timeline...",
                    "Select a customer to view equipment...",
                    go.Figure(),
                    "Select a customer for quality analysis..."
                )

            # Extract customer ID from selection
            customer_id = int(customer_selection.split(':')[0])
            logger.info(f"Loading customer profile: {customer_id}")

            # Mock customer data (replace with actual database query)
            customer_data = {
                'id': customer_id,
                'name': 'Jan Kowalski',
                'email': '<EMAIL>',
                'phone': '+48123456789',
                'address': 'ul. Testowa 123, Warszawa',
                'status': 'Active',
                'quality_score': 0.85,
                'created_date': '2023-06-15',
                'last_interaction': '2024-01-15',
                'total_value': 15750.00,
                'interaction_count': 12,
                'equipment_count': 2
            }

            # Generate profile HTML
            profile_html = self._generate_customer_profile_html(customer_data)

            # Generate timeline
            timeline_html = self._generate_interaction_timeline(customer_id)

            # Generate equipment registry
            equipment_html = self._generate_equipment_registry(customer_id)

            # Generate quality plot
            quality_plot = self._generate_quality_plot(customer_data)

            # Generate quality details
            quality_details = self._generate_quality_details(customer_data)

            return profile_html, timeline_html, equipment_html, quality_plot, quality_details

        except Exception as e:
            logger.error(f"Error loading customer profile: {e}")
            error_html = f"<div class='data-quality-low'>Error loading profile: {str(e)}</div>"
            return error_html, error_html, error_html, go.Figure(), error_html

    def _generate_customer_profile_html(self, customer_data: Dict) -> str:
        """Generate customer profile HTML."""
        quality_class = self._get_quality_class(customer_data['quality_score'])

        return f"""
        <div class="customer-profile-card">
            <div class="customer-header">
                <h3 style="margin: 0 0 8px 0;">👤 {customer_data['name']}</h3>
                <p style="margin: 0; opacity: 0.9;">Customer ID: {customer_data['id']} • Status: {customer_data['status']}</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="margin: 0 0 12px 0; color: #1a73e8;">📞 Contact Information</h4>
                    <p style="margin: 0 0 8px 0;"><strong>Email:</strong> {customer_data['email']}</p>
                    <p style="margin: 0 0 8px 0;"><strong>Phone:</strong> {customer_data['phone']}</p>
                    <p style="margin: 0 0 8px 0;"><strong>Address:</strong> {customer_data['address']}</p>
                </div>

                <div>
                    <h4 style="margin: 0 0 12px 0; color: #1a73e8;">📊 Customer Metrics</h4>
                    <p style="margin: 0 0 8px 0;"><strong>Total Value:</strong> {customer_data['total_value']:,.2f} PLN</p>
                    <p style="margin: 0 0 8px 0;"><strong>Interactions:</strong> {customer_data['interaction_count']}</p>
                    <p style="margin: 0 0 8px 0;"><strong>Equipment:</strong> {customer_data['equipment_count']} units</p>
                </div>

                <div>
                    <h4 style="margin: 0 0 12px 0; color: #1a73e8;">📅 Timeline</h4>
                    <p style="margin: 0 0 8px 0;"><strong>Customer Since:</strong> {customer_data['created_date']}</p>
                    <p style="margin: 0 0 8px 0;"><strong>Last Contact:</strong> {customer_data['last_interaction']}</p>
                    <div class="{quality_class}" style="padding: 8px; border-radius: 8px; margin-top: 8px;">
                        <strong>Data Quality:</strong> {customer_data['quality_score']:.1%}
                    </div>
                </div>
            </div>
        </div>
        """
