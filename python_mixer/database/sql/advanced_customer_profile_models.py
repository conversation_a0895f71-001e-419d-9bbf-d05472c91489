#!/usr/bin/env python3
"""
Advanced Customer Profile Models for HVAC CRM
Comprehensive data model ensuring full data consistency and no data loss.

Features:
- Unified customer profiles with all interactions
- Calendar event processing and linking
- Email and transcription integration
- Equipment registry with service history
- Data deduplication and consistency checks
- Full audit trail for all changes
"""

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, Float, 
    ForeignKey, UniqueConstraint, Index, CheckConstraint,
    JSON, DECIMAL, Enum as SQLEnum
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, backref
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum
import uuid

Base = declarative_base()


class CustomerStatus(Enum):
    """Customer status enumeration."""
    PROSPECT = "prospect"
    ACTIVE = "active"
    INACTIVE = "inactive"
    VIP = "vip"
    BLOCKED = "blocked"


class InteractionType(Enum):
    """Interaction type enumeration."""
    EMAIL = "email"
    PHONE_CALL = "phone_call"
    CALENDAR_EVENT = "calendar_event"
    TRANSCRIPTION = "transcription"
    SITE_VISIT = "site_visit"
    SERVICE_CALL = "service_call"
    QUOTE_REQUEST = "quote_request"
    COMPLAINT = "complaint"


class ServiceType(Enum):
    """Service type enumeration."""
    INSTALLATION = "installation"
    REPAIR = "repair"
    MAINTENANCE = "maintenance"
    INSPECTION = "inspection"
    CONSULTATION = "consultation"
    WARRANTY = "warranty"
    EMERGENCY = "emergency"


class Customer(Base):
    """
    Unified customer profile - central hub for all customer data.
    Ensures no data loss and full consistency across all interactions.
    """
    __tablename__ = 'customers'

    id = Column(Integer, primary_key=True, autoincrement=True)
    uuid = Column(String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    
    # Basic Information
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    company_name = Column(String(200), nullable=True)
    
    # Primary Contact (most recent/reliable)
    primary_email = Column(String(255), nullable=True)
    primary_phone = Column(String(20), nullable=True)
    primary_address_id = Column(Integer, ForeignKey('customer_addresses.id'), nullable=True)
    
    # Status and Classification
    status = Column(SQLEnum(CustomerStatus), default=CustomerStatus.PROSPECT, nullable=False)
    customer_type = Column(String(50), default='individual')  # individual, business, property_manager
    
    # Business Intelligence
    total_value = Column(DECIMAL(10, 2), default=0.00)
    interaction_count = Column(Integer, default=0)
    last_interaction_date = Column(DateTime, nullable=True)
    acquisition_source = Column(String(100), nullable=True)  # email, calendar, referral, website
    
    # Data Quality and Deduplication
    confidence_score = Column(Float, default=0.5)  # 0.0-1.0 confidence in data accuracy
    duplicate_of_id = Column(Integer, ForeignKey('customers.id'), nullable=True)
    is_duplicate = Column(Boolean, default=False)
    
    # Metadata
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    created_by = Column(String(100), default='system')
    notes = Column(Text, nullable=True)
    
    # Relationships
    contacts = relationship("CustomerContact", back_populates="customer", cascade="all, delete-orphan")
    addresses = relationship("CustomerAddress", back_populates="customer", cascade="all, delete-orphan")
    interactions = relationship("CustomerInteraction", back_populates="customer", cascade="all, delete-orphan")
    equipment = relationship("CustomerEquipment", back_populates="customer", cascade="all, delete-orphan")
    calendar_events = relationship("CalendarEvent", back_populates="customer", cascade="all, delete-orphan")
    
    # Self-referential relationship for duplicates
    duplicates = relationship("Customer", remote_side=[id], backref="original_customer")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_customer_email', 'primary_email'),
        Index('idx_customer_phone', 'primary_phone'),
        Index('idx_customer_name', 'first_name', 'last_name'),
        Index('idx_customer_company', 'company_name'),
        Index('idx_customer_status', 'status'),
        Index('idx_customer_updated', 'updated_at'),
        Index('idx_customer_uuid', 'uuid'),
    )


class CustomerContact(Base):
    """
    Customer contact information - supports multiple emails/phones per customer.
    Tracks source and reliability of each contact method.
    """
    __tablename__ = 'customer_contacts'

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(Integer, ForeignKey('customers.id', ondelete='CASCADE'), nullable=False)
    
    contact_type = Column(String(20), nullable=False)  # email, phone, fax
    contact_value = Column(String(255), nullable=False)
    
    # Metadata
    is_primary = Column(Boolean, default=False)
    is_verified = Column(Boolean, default=False)
    source = Column(String(100), nullable=True)  # email_analysis, calendar_event, manual_entry
    confidence_score = Column(Float, default=0.5)
    
    created_at = Column(DateTime, default=func.now(), nullable=False)
    last_used_at = Column(DateTime, nullable=True)
    usage_count = Column(Integer, default=0)
    
    # Relationships
    customer = relationship("Customer", back_populates="contacts")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('customer_id', 'contact_type', 'contact_value', name='uq_customer_contact'),
        Index('idx_contact_value', 'contact_value'),
        Index('idx_contact_type', 'contact_type'),
        CheckConstraint('confidence_score >= 0.0 AND confidence_score <= 1.0', name='ck_contact_confidence'),
    )


class CustomerAddress(Base):
    """
    Customer addresses with geocoding and standardization.
    Supports multiple addresses per customer (home, work, service locations).
    """
    __tablename__ = 'customer_addresses'

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(Integer, ForeignKey('customers.id', ondelete='CASCADE'), nullable=False)
    
    # Address Components
    street_address = Column(String(255), nullable=False)
    city = Column(String(100), nullable=False)
    postal_code = Column(String(10), nullable=True)
    country = Column(String(50), default='Poland', nullable=False)
    
    # Address Classification
    address_type = Column(String(50), default='primary')  # primary, billing, service, work
    is_primary = Column(Boolean, default=False)
    
    # Geocoding and Standardization
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    standardized_address = Column(String(500), nullable=True)
    geocoding_confidence = Column(Float, default=0.0)
    
    # Metadata
    source = Column(String(100), nullable=True)
    confidence_score = Column(Float, default=0.5)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # Relationships
    customer = relationship("Customer", back_populates="addresses")
    
    # Constraints and Indexes
    __table_args__ = (
        Index('idx_address_city', 'city'),
        Index('idx_address_postal', 'postal_code'),
        Index('idx_address_coords', 'latitude', 'longitude'),
        CheckConstraint('confidence_score >= 0.0 AND confidence_score <= 1.0', name='ck_address_confidence'),
    )


class CustomerInteraction(Base):
    """
    Unified interaction log - all customer touchpoints in one place.
    Links emails, calendar events, transcriptions, and manual entries.
    """
    __tablename__ = 'customer_interactions'

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(Integer, ForeignKey('customers.id', ondelete='CASCADE'), nullable=False)
    
    # Interaction Classification
    interaction_type = Column(SQLEnum(InteractionType), nullable=False)
    subject = Column(String(500), nullable=True)
    content = Column(Text, nullable=True)
    
    # Source Information
    source_id = Column(String(255), nullable=True)  # email_id, calendar_event_id, etc.
    source_system = Column(String(100), nullable=True)  # email_parser, calendar_agent, manual
    
    # Timing
    interaction_date = Column(DateTime, nullable=False)
    duration_minutes = Column(Integer, nullable=True)
    
    # Business Context
    service_type = Column(SQLEnum(ServiceType), nullable=True)
    priority_level = Column(String(20), default='medium')  # low, medium, high, urgent
    sentiment_score = Column(Float, nullable=True)  # -1.0 to 1.0
    
    # AI Analysis Results
    extracted_entities = Column(JSON, nullable=True)  # Equipment, addresses, contacts, etc.
    ai_summary = Column(Text, nullable=True)
    confidence_score = Column(Float, default=0.5)
    
    # Follow-up and Status
    requires_followup = Column(Boolean, default=False)
    followup_date = Column(DateTime, nullable=True)
    status = Column(String(50), default='open')  # open, in_progress, completed, cancelled
    
    # Metadata
    created_at = Column(DateTime, default=func.now(), nullable=False)
    created_by = Column(String(100), default='system')
    
    # Relationships
    customer = relationship("Customer", back_populates="interactions")
    
    # Indexes
    __table_args__ = (
        Index('idx_interaction_date', 'interaction_date'),
        Index('idx_interaction_type', 'interaction_type'),
        Index('idx_interaction_priority', 'priority_level'),
        Index('idx_interaction_status', 'status'),
        Index('idx_interaction_followup', 'followup_date'),
        CheckConstraint('confidence_score >= 0.0 AND confidence_score <= 1.0', name='ck_interaction_confidence'),
        CheckConstraint('sentiment_score >= -1.0 AND sentiment_score <= 1.0', name='ck_sentiment_range'),
    )


class CalendarEvent(Base):
    """
    Calendar events with advanced parsing and customer linking.
    Extracts structured data from unstructured calendar descriptions.
    """
    __tablename__ = 'calendar_events'

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(Integer, ForeignKey('customers.id', ondelete='CASCADE'), nullable=True)

    # Event Information
    title = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    start_datetime = Column(DateTime, nullable=False)
    end_datetime = Column(DateTime, nullable=True)
    location = Column(String(500), nullable=True)

    # Parsed Information (extracted by AI)
    parsed_customer_name = Column(String(200), nullable=True)
    parsed_phone = Column(String(20), nullable=True)
    parsed_address = Column(String(500), nullable=True)
    parsed_service_type = Column(SQLEnum(ServiceType), nullable=True)
    parsed_equipment = Column(JSON, nullable=True)  # List of equipment mentioned

    # Processing Status
    is_processed = Column(Boolean, default=False)
    processing_confidence = Column(Float, default=0.0)
    needs_manual_review = Column(Boolean, default=False)

    # Source and Metadata
    source_calendar = Column(String(100), nullable=True)  # google, outlook, etc.
    external_id = Column(String(255), nullable=True)  # Original calendar event ID
    created_at = Column(DateTime, default=func.now(), nullable=False)
    processed_at = Column(DateTime, nullable=True)

    # Relationships
    customer = relationship("Customer", back_populates="calendar_events")

    # Indexes
    __table_args__ = (
        Index('idx_calendar_start', 'start_datetime'),
        Index('idx_calendar_processed', 'is_processed'),
        Index('idx_calendar_review', 'needs_manual_review'),
        Index('idx_calendar_customer', 'customer_id'),
        CheckConstraint('processing_confidence >= 0.0 AND processing_confidence <= 1.0', name='ck_calendar_confidence'),
    )


class CustomerEquipment(Base):
    """
    Equipment registry linked to customers with service history.
    Tracks all HVAC equipment per customer location.
    """
    __tablename__ = 'customer_equipment'

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(Integer, ForeignKey('customers.id', ondelete='CASCADE'), nullable=False)
    address_id = Column(Integer, ForeignKey('customer_addresses.id'), nullable=True)

    # Equipment Information
    brand = Column(String(100), nullable=True)  # LG, Daikin, etc.
    model = Column(String(100), nullable=True)  # S12ET, etc.
    equipment_type = Column(String(100), nullable=True)  # split, multi-split, VRF
    serial_number = Column(String(100), nullable=True)

    # Installation Details
    installation_date = Column(DateTime, nullable=True)
    warranty_end_date = Column(DateTime, nullable=True)
    installer_notes = Column(Text, nullable=True)

    # Current Status
    status = Column(String(50), default='active')  # active, inactive, replaced, removed
    last_service_date = Column(DateTime, nullable=True)
    next_service_due = Column(DateTime, nullable=True)

    # Technical Specifications
    capacity_btu = Column(Integer, nullable=True)
    energy_rating = Column(String(10), nullable=True)
    refrigerant_type = Column(String(20), nullable=True)

    # Source and Confidence
    source = Column(String(100), nullable=True)  # calendar_event, email_analysis, manual
    confidence_score = Column(Float, default=0.5)

    # Metadata
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    customer = relationship("Customer", back_populates="equipment")
    address = relationship("CustomerAddress")

    # Indexes
    __table_args__ = (
        Index('idx_equipment_brand_model', 'brand', 'model'),
        Index('idx_equipment_serial', 'serial_number'),
        Index('idx_equipment_status', 'status'),
        Index('idx_equipment_service_due', 'next_service_due'),
        CheckConstraint('confidence_score >= 0.0 AND confidence_score <= 1.0', name='ck_equipment_confidence'),
    )


class DataAuditLog(Base):
    """
    Comprehensive audit trail for all data changes.
    Ensures full traceability and data integrity.
    """
    __tablename__ = 'data_audit_log'

    id = Column(Integer, primary_key=True, autoincrement=True)

    # What was changed
    table_name = Column(String(100), nullable=False)
    record_id = Column(Integer, nullable=False)
    operation = Column(String(20), nullable=False)  # INSERT, UPDATE, DELETE, MERGE

    # Change details
    old_values = Column(JSON, nullable=True)
    new_values = Column(JSON, nullable=True)
    changed_fields = Column(JSON, nullable=True)  # List of field names that changed

    # Context
    change_reason = Column(String(500), nullable=True)
    source_system = Column(String(100), nullable=True)
    confidence_score = Column(Float, nullable=True)

    # Who and when
    created_at = Column(DateTime, default=func.now(), nullable=False)
    created_by = Column(String(100), nullable=False)

    # Indexes
    __table_args__ = (
        Index('idx_audit_table_record', 'table_name', 'record_id'),
        Index('idx_audit_created', 'created_at'),
        Index('idx_audit_operation', 'operation'),
        Index('idx_audit_user', 'created_by'),
    )


class CustomerDuplicateCandidate(Base):
    """
    Potential customer duplicates for manual review.
    Helps maintain data quality and prevent duplicate entries.
    """
    __tablename__ = 'customer_duplicate_candidates'

    id = Column(Integer, primary_key=True, autoincrement=True)

    # Duplicate pair
    customer1_id = Column(Integer, ForeignKey('customers.id', ondelete='CASCADE'), nullable=False)
    customer2_id = Column(Integer, ForeignKey('customers.id', ondelete='CASCADE'), nullable=False)

    # Similarity metrics
    similarity_score = Column(Float, nullable=False)  # 0.0-1.0
    matching_fields = Column(JSON, nullable=True)  # Which fields match
    confidence_level = Column(String(20), default='medium')  # low, medium, high

    # Review status
    status = Column(String(20), default='pending')  # pending, reviewed, merged, dismissed
    reviewed_by = Column(String(100), nullable=True)
    reviewed_at = Column(DateTime, nullable=True)
    merge_decision = Column(String(20), nullable=True)  # merge_to_1, merge_to_2, keep_separate

    # Metadata
    created_at = Column(DateTime, default=func.now(), nullable=False)
    detection_method = Column(String(100), nullable=True)  # email_similarity, phone_match, etc.

    # Relationships
    customer1 = relationship("Customer", foreign_keys=[customer1_id])
    customer2 = relationship("Customer", foreign_keys=[customer2_id])

    # Constraints
    __table_args__ = (
        UniqueConstraint('customer1_id', 'customer2_id', name='uq_duplicate_pair'),
        Index('idx_duplicate_score', 'similarity_score'),
        Index('idx_duplicate_status', 'status'),
        CheckConstraint('similarity_score >= 0.0 AND similarity_score <= 1.0', name='ck_similarity_score'),
        CheckConstraint('customer1_id != customer2_id', name='ck_different_customers'),
    )


# Database utility functions
def create_all_tables(engine):
    """Create all tables with proper constraints and indexes."""
    Base.metadata.create_all(engine)


def get_customer_unified_profile(session, customer_id: int) -> dict:
    """
    Get complete unified customer profile with all related data.
    Returns comprehensive customer information for human comprehension.
    """
    customer = session.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        return None

    return {
        'customer': customer,
        'contacts': customer.contacts,
        'addresses': customer.addresses,
        'interactions': customer.interactions,
        'equipment': customer.equipment,
        'calendar_events': customer.calendar_events,
        'interaction_timeline': sorted(customer.interactions, key=lambda x: x.interaction_date, reverse=True),
        'total_interactions': len(customer.interactions),
        'last_contact': customer.interactions[0].interaction_date if customer.interactions else None,
        'equipment_count': len(customer.equipment),
        'active_equipment': [eq for eq in customer.equipment if eq.status == 'active']
    }
