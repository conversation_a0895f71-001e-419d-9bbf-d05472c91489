#!/usr/bin/env python3
"""
Customer Unification Engine for HVAC CRM
Advanced engine for merging customer data from multiple sources with full data consistency.

Features:
- Email analysis integration
- Calendar event processing
- Transcription data linking
- Duplicate detection and merging
- Data quality scoring
- Audit trail maintenance
- Fuzzy matching algorithms
"""

import asyncio
import json
from typing import Dict, List, Optional, Tuple, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from difflib import SequenceMatcher
import re
from loguru import logger

try:
    from ..database.sql.advanced_customer_profile_models import (
        Customer, CustomerContact, CustomerAddress, CustomerInteraction,
        CustomerEquipment, CalendarEvent, DataAuditLog, CustomerDuplicateCandidate,
        InteractionType, ServiceType, CustomerStatus, get_customer_unified_profile
    )
    from ..database.sql.models import db_manager
    from ..agents.calendar_processing_agent import CalendarProcessingAgent, ExtractedCalendarData
except ImportError:
    logger.warning("Database models not available - running in standalone mode")


@dataclass
class CustomerMatchResult:
    """Result of customer matching operation."""
    customer_id: Optional[int] = None
    match_confidence: float = 0.0
    match_type: str = 'none'  # exact, fuzzy, new
    matching_fields: List[str] = None
    suggested_merges: List[int] = None

    def __post_init__(self):
        if self.matching_fields is None:
            self.matching_fields = []
        if self.suggested_merges is None:
            self.suggested_merges = []


@dataclass
class UnificationResult:
    """Result of customer data unification."""
    customer_id: int
    operations_performed: List[str]
    data_quality_score: float
    confidence_score: float
    audit_log_ids: List[int]
    warnings: List[str] = None
    errors: List[str] = None

    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []
        if self.errors is None:
            self.errors = []


class CustomerUnificationEngine:
    """
    Advanced engine for unifying customer data from multiple sources.
    
    Ensures data consistency and prevents data loss while maintaining
    full audit trail of all operations.
    """

    def __init__(self):
        self.calendar_agent = CalendarProcessingAgent()
        self.similarity_threshold = 0.8
        self.fuzzy_threshold = 0.6
        self.phone_weight = 0.4
        self.email_weight = 0.3
        self.name_weight = 0.2
        self.address_weight = 0.1
        
        logger.info("Customer Unification Engine initialized")

    async def process_email_analysis(
        self, 
        email_data: Dict[str, Any], 
        analysis_result: Dict[str, Any]
    ) -> UnificationResult:
        """
        Process email analysis results and create/update customer profile.
        
        Args:
            email_data: Raw email data
            analysis_result: AI analysis results from email processing
            
        Returns:
            UnificationResult with customer ID and operations performed
        """
        try:
            logger.info("Processing email analysis for customer unification")
            
            # Extract customer information from email and analysis
            customer_info = self._extract_customer_from_email(email_data, analysis_result)
            
            # Find or create customer
            match_result = await self._find_matching_customer(customer_info)
            
            if match_result.customer_id:
                # Update existing customer
                customer_id = match_result.customer_id
                operations = await self._update_customer_from_email(
                    customer_id, customer_info, email_data, analysis_result
                )
            else:
                # Create new customer
                customer_id = await self._create_customer_from_email(
                    customer_info, email_data, analysis_result
                )
                operations = ['create_customer', 'add_email_interaction']
            
            # Calculate data quality score
            quality_score = await self._calculate_data_quality_score(customer_id)
            
            # Create audit log
            audit_ids = await self._create_audit_logs(
                customer_id, operations, 'email_analysis', analysis_result
            )
            
            return UnificationResult(
                customer_id=customer_id,
                operations_performed=operations,
                data_quality_score=quality_score,
                confidence_score=match_result.match_confidence,
                audit_log_ids=audit_ids
            )
            
        except Exception as e:
            logger.error(f"Error processing email analysis: {e}")
            return UnificationResult(
                customer_id=0,
                operations_performed=[],
                data_quality_score=0.0,
                confidence_score=0.0,
                audit_log_ids=[],
                errors=[str(e)]
            )

    async def process_calendar_event(
        self, 
        calendar_data: Dict[str, Any]
    ) -> UnificationResult:
        """
        Process calendar event and create/update customer profile.
        
        Args:
            calendar_data: Calendar event data
            
        Returns:
            UnificationResult with customer ID and operations performed
        """
        try:
            logger.info("Processing calendar event for customer unification")
            
            # Extract structured data from calendar event
            extracted_data = await self.calendar_agent.process_calendar_event(calendar_data)
            
            if extracted_data.confidence_score < 0.3:
                logger.warning("Low confidence calendar extraction - flagging for manual review")
                # Create calendar event record for manual review
                calendar_id = await self._create_calendar_event_record(
                    calendar_data, extracted_data, needs_review=True
                )
                return UnificationResult(
                    customer_id=0,
                    operations_performed=['create_calendar_event_for_review'],
                    data_quality_score=0.0,
                    confidence_score=extracted_data.confidence_score,
                    audit_log_ids=[],
                    warnings=['Low confidence extraction - manual review required']
                )
            
            # Convert extracted data to customer info format
            customer_info = self._convert_calendar_to_customer_info(extracted_data)
            
            # Find or create customer
            match_result = await self._find_matching_customer(customer_info)
            
            if match_result.customer_id:
                # Update existing customer
                customer_id = match_result.customer_id
                operations = await self._update_customer_from_calendar(
                    customer_id, customer_info, calendar_data, extracted_data
                )
            else:
                # Create new customer
                customer_id = await self._create_customer_from_calendar(
                    customer_info, calendar_data, extracted_data
                )
                operations = ['create_customer', 'add_calendar_interaction']
            
            # Link calendar event to customer
            await self._link_calendar_event_to_customer(
                customer_id, calendar_data, extracted_data
            )
            operations.append('link_calendar_event')
            
            # Calculate data quality score
            quality_score = await self._calculate_data_quality_score(customer_id)
            
            # Create audit log
            audit_ids = await self._create_audit_logs(
                customer_id, operations, 'calendar_processing', extracted_data.__dict__
            )
            
            return UnificationResult(
                customer_id=customer_id,
                operations_performed=operations,
                data_quality_score=quality_score,
                confidence_score=extracted_data.confidence_score,
                audit_log_ids=audit_ids
            )
            
        except Exception as e:
            logger.error(f"Error processing calendar event: {e}")
            return UnificationResult(
                customer_id=0,
                operations_performed=[],
                data_quality_score=0.0,
                confidence_score=0.0,
                audit_log_ids=[],
                errors=[str(e)]
            )

    async def process_transcription_data(
        self, 
        transcription_data: Dict[str, Any],
        analysis_result: Dict[str, Any]
    ) -> UnificationResult:
        """
        Process transcription data and link to customer profile.
        
        Args:
            transcription_data: Audio transcription data
            analysis_result: AI analysis of transcription
            
        Returns:
            UnificationResult with customer ID and operations performed
        """
        try:
            logger.info("Processing transcription data for customer unification")
            
            # Extract customer information from transcription
            customer_info = self._extract_customer_from_transcription(
                transcription_data, analysis_result
            )
            
            # Find or create customer
            match_result = await self._find_matching_customer(customer_info)
            
            if match_result.customer_id:
                # Update existing customer
                customer_id = match_result.customer_id
                operations = await self._update_customer_from_transcription(
                    customer_id, customer_info, transcription_data, analysis_result
                )
            else:
                # Create new customer
                customer_id = await self._create_customer_from_transcription(
                    customer_info, transcription_data, analysis_result
                )
                operations = ['create_customer', 'add_transcription_interaction']
            
            # Calculate data quality score
            quality_score = await self._calculate_data_quality_score(customer_id)
            
            # Create audit log
            audit_ids = await self._create_audit_logs(
                customer_id, operations, 'transcription_processing', analysis_result
            )
            
            return UnificationResult(
                customer_id=customer_id,
                operations_performed=operations,
                data_quality_score=quality_score,
                confidence_score=match_result.match_confidence,
                audit_log_ids=audit_ids
            )
            
        except Exception as e:
            logger.error(f"Error processing transcription data: {e}")
            return UnificationResult(
                customer_id=0,
                operations_performed=[],
                data_quality_score=0.0,
                confidence_score=0.0,
                audit_log_ids=[],
                errors=[str(e)]
            )

    async def detect_and_merge_duplicates(self) -> List[Dict[str, Any]]:
        """
        Detect potential duplicate customers and suggest merges.
        
        Returns:
            List of duplicate candidates with similarity scores
        """
        try:
            logger.info("Starting duplicate detection process")
            
            # Get all active customers
            session = db_manager.get_session()
            customers = session.query(Customer).filter(
                Customer.is_duplicate == False,
                Customer.status != CustomerStatus.BLOCKED
            ).all()
            
            duplicate_candidates = []
            
            # Compare each customer with others
            for i, customer1 in enumerate(customers):
                for customer2 in customers[i+1:]:
                    similarity = self._calculate_customer_similarity(customer1, customer2)
                    
                    if similarity > self.fuzzy_threshold:
                        candidate = {
                            'customer1_id': customer1.id,
                            'customer2_id': customer2.id,
                            'similarity_score': similarity,
                            'matching_fields': self._get_matching_fields(customer1, customer2),
                            'confidence_level': 'high' if similarity > self.similarity_threshold else 'medium'
                        }
                        duplicate_candidates.append(candidate)
                        
                        # Store in database for review
                        await self._store_duplicate_candidate(candidate)
            
            session.close()
            
            logger.info(f"Found {len(duplicate_candidates)} potential duplicates")
            return duplicate_candidates
            
        except Exception as e:
            logger.error(f"Error in duplicate detection: {e}")
            return []

    def _extract_customer_from_email(
        self, 
        email_data: Dict[str, Any], 
        analysis_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract customer information from email data and analysis."""
        customer_info = {}
        
        # Extract from email headers
        if 'from' in email_data:
            email_address = email_data['from']
            customer_info['email'] = email_address
            
            # Try to extract name from email
            name_match = re.search(r'^([^<]+)<', email_address)
            if name_match:
                customer_info['name'] = name_match.group(1).strip()
        
        # Extract from analysis results
        if 'extracted_entities' in analysis_result:
            entities = analysis_result['extracted_entities']
            
            if 'contacts' in entities:
                customer_info['phone'] = entities['contacts'][0] if entities['contacts'] else None
            
            if 'locations' in entities:
                customer_info['address'] = entities['locations'][0] if entities['locations'] else None
            
            if 'equipment' in entities:
                customer_info['equipment'] = entities['equipment']
        
        # Extract service type from content analysis
        if 'content_analysis' in analysis_result:
            content = analysis_result['content_analysis']
            customer_info['service_type'] = content.get('email_type')
            customer_info['priority'] = content.get('priority_level')
            customer_info['sentiment'] = content.get('sentiment_score')
        
        return customer_info

    async def _find_matching_customer(self, customer_info: Dict[str, Any]) -> CustomerMatchResult:
        """Find matching customer using fuzzy matching algorithms."""
        try:
            session = db_manager.get_session()

            # Exact matches first
            exact_match = None

            # Check by email
            if customer_info.get('email'):
                exact_match = session.query(Customer).join(CustomerContact).filter(
                    CustomerContact.contact_type == 'email',
                    CustomerContact.contact_value == customer_info['email']
                ).first()

            # Check by phone
            if not exact_match and customer_info.get('phone'):
                phone = self._normalize_phone(customer_info['phone'])
                exact_match = session.query(Customer).join(CustomerContact).filter(
                    CustomerContact.contact_type == 'phone',
                    CustomerContact.contact_value == phone
                ).first()

            if exact_match:
                session.close()
                return CustomerMatchResult(
                    customer_id=exact_match.id,
                    match_confidence=1.0,
                    match_type='exact',
                    matching_fields=['email' if customer_info.get('email') else 'phone']
                )

            # Fuzzy matching
            customers = session.query(Customer).filter(
                Customer.is_duplicate == False
            ).limit(1000).all()  # Limit for performance

            best_match = None
            best_score = 0.0

            for customer in customers:
                score = self._calculate_customer_similarity_with_info(customer, customer_info)
                if score > best_score and score > self.fuzzy_threshold:
                    best_match = customer
                    best_score = score

            session.close()

            if best_match:
                return CustomerMatchResult(
                    customer_id=best_match.id,
                    match_confidence=best_score,
                    match_type='fuzzy',
                    matching_fields=self._get_matching_fields_with_info(best_match, customer_info)
                )

            # No match found
            return CustomerMatchResult(match_type='new')

        except Exception as e:
            logger.error(f"Error finding matching customer: {e}")
            return CustomerMatchResult(match_type='new')

    def _calculate_customer_similarity(self, customer1: Customer, customer2: Customer) -> float:
        """Calculate similarity score between two customers."""
        score = 0.0

        # Phone similarity
        phone1 = self._get_primary_contact(customer1, 'phone')
        phone2 = self._get_primary_contact(customer2, 'phone')
        if phone1 and phone2:
            if phone1 == phone2:
                score += self.phone_weight
            elif self._phones_similar(phone1, phone2):
                score += self.phone_weight * 0.8

        # Email similarity
        email1 = self._get_primary_contact(customer1, 'email')
        email2 = self._get_primary_contact(customer2, 'email')
        if email1 and email2:
            if email1.lower() == email2.lower():
                score += self.email_weight
            elif self._emails_similar(email1, email2):
                score += self.email_weight * 0.7

        # Name similarity
        name1 = f"{customer1.first_name or ''} {customer1.last_name or ''}".strip()
        name2 = f"{customer2.first_name or ''} {customer2.last_name or ''}".strip()
        if name1 and name2:
            name_sim = SequenceMatcher(None, name1.lower(), name2.lower()).ratio()
            score += self.name_weight * name_sim

        # Address similarity
        addr1 = self._get_primary_address(customer1)
        addr2 = self._get_primary_address(customer2)
        if addr1 and addr2:
            addr_sim = SequenceMatcher(None, addr1.lower(), addr2.lower()).ratio()
            score += self.address_weight * addr_sim

        return min(score, 1.0)

    def _calculate_customer_similarity_with_info(
        self,
        customer: Customer,
        customer_info: Dict[str, Any]
    ) -> float:
        """Calculate similarity between existing customer and extracted info."""
        score = 0.0

        # Phone similarity
        if customer_info.get('phone'):
            customer_phone = self._get_primary_contact(customer, 'phone')
            if customer_phone:
                info_phone = self._normalize_phone(customer_info['phone'])
                if customer_phone == info_phone:
                    score += self.phone_weight
                elif self._phones_similar(customer_phone, info_phone):
                    score += self.phone_weight * 0.8

        # Email similarity
        if customer_info.get('email'):
            customer_email = self._get_primary_contact(customer, 'email')
            if customer_email:
                if customer_email.lower() == customer_info['email'].lower():
                    score += self.email_weight
                elif self._emails_similar(customer_email, customer_info['email']):
                    score += self.email_weight * 0.7

        # Name similarity
        if customer_info.get('name'):
            customer_name = f"{customer.first_name or ''} {customer.last_name or ''}".strip()
            if customer_name:
                name_sim = SequenceMatcher(None, customer_name.lower(), customer_info['name'].lower()).ratio()
                score += self.name_weight * name_sim

        # Address similarity
        if customer_info.get('address'):
            customer_addr = self._get_primary_address(customer)
            if customer_addr:
                addr_sim = SequenceMatcher(None, customer_addr.lower(), customer_info['address'].lower()).ratio()
                score += self.address_weight * addr_sim

        return min(score, 1.0)

    def _normalize_phone(self, phone: str) -> str:
        """Normalize phone number to standard format."""
        if not phone:
            return ""

        # Remove all non-digits except +
        phone = re.sub(r'[^\d+]', '', phone)

        # Handle Polish numbers
        if phone.startswith('+48'):
            return phone
        elif phone.startswith('48') and len(phone) == 11:
            return f'+{phone}'
        elif len(phone) == 9:
            return f'+48{phone}'

        return phone

    def _phones_similar(self, phone1: str, phone2: str) -> bool:
        """Check if two phone numbers are similar (accounting for formatting)."""
        norm1 = self._normalize_phone(phone1)
        norm2 = self._normalize_phone(phone2)
        return norm1 == norm2

    def _emails_similar(self, email1: str, email2: str) -> bool:
        """Check if two emails are similar."""
        # Simple domain check for now
        domain1 = email1.split('@')[-1] if '@' in email1 else ''
        domain2 = email2.split('@')[-1] if '@' in email2 else ''

        # Same domain and similar local part
        if domain1 == domain2:
            local1 = email1.split('@')[0]
            local2 = email2.split('@')[0]
            return SequenceMatcher(None, local1, local2).ratio() > 0.8

        return False

    def _get_primary_contact(self, customer: Customer, contact_type: str) -> Optional[str]:
        """Get primary contact of specified type for customer."""
        for contact in customer.contacts:
            if contact.contact_type == contact_type and contact.is_primary:
                return contact.contact_value

        # If no primary, return first of type
        for contact in customer.contacts:
            if contact.contact_type == contact_type:
                return contact.contact_value

        return None

    def _get_primary_address(self, customer: Customer) -> Optional[str]:
        """Get primary address for customer."""
        for address in customer.addresses:
            if address.is_primary:
                return address.street_address

        # If no primary, return first address
        if customer.addresses:
            return customer.addresses[0].street_address

        return None

    def _get_matching_fields(self, customer1: Customer, customer2: Customer) -> List[str]:
        """Get list of fields that match between two customers."""
        matching = []

        # Check phone
        phone1 = self._get_primary_contact(customer1, 'phone')
        phone2 = self._get_primary_contact(customer2, 'phone')
        if phone1 and phone2 and self._phones_similar(phone1, phone2):
            matching.append('phone')

        # Check email
        email1 = self._get_primary_contact(customer1, 'email')
        email2 = self._get_primary_contact(customer2, 'email')
        if email1 and email2 and email1.lower() == email2.lower():
            matching.append('email')

        # Check name
        name1 = f"{customer1.first_name or ''} {customer1.last_name or ''}".strip()
        name2 = f"{customer2.first_name or ''} {customer2.last_name or ''}".strip()
        if name1 and name2:
            name_sim = SequenceMatcher(None, name1.lower(), name2.lower()).ratio()
            if name_sim > 0.8:
                matching.append('name')

        return matching

    def _get_matching_fields_with_info(
        self,
        customer: Customer,
        customer_info: Dict[str, Any]
    ) -> List[str]:
        """Get list of fields that match between customer and extracted info."""
        matching = []

        # Check phone
        if customer_info.get('phone'):
            customer_phone = self._get_primary_contact(customer, 'phone')
            if customer_phone and self._phones_similar(customer_phone, customer_info['phone']):
                matching.append('phone')

        # Check email
        if customer_info.get('email'):
            customer_email = self._get_primary_contact(customer, 'email')
            if customer_email and customer_email.lower() == customer_info['email'].lower():
                matching.append('email')

        # Check name
        if customer_info.get('name'):
            customer_name = f"{customer.first_name or ''} {customer.last_name or ''}".strip()
            if customer_name:
                name_sim = SequenceMatcher(None, customer_name.lower(), customer_info['name'].lower()).ratio()
                if name_sim > 0.8:
                    matching.append('name')

        return matching

    async def _calculate_data_quality_score(self, customer_id: int) -> float:
        """Calculate data quality score for customer profile."""
        try:
            session = db_manager.get_session()
            customer = session.query(Customer).filter(Customer.id == customer_id).first()

            if not customer:
                return 0.0

            score = 0.0
            max_score = 10.0

            # Basic information completeness (4 points)
            if customer.first_name and customer.last_name:
                score += 2.0
            elif customer.first_name or customer.last_name:
                score += 1.0

            if customer.primary_email:
                score += 1.0

            if customer.primary_phone:
                score += 1.0

            # Contact information quality (2 points)
            verified_contacts = sum(1 for c in customer.contacts if c.is_verified)
            score += min(verified_contacts * 0.5, 2.0)

            # Address information (1 point)
            if customer.addresses:
                score += 1.0

            # Interaction history (2 points)
            interaction_count = len(customer.interactions)
            score += min(interaction_count * 0.2, 2.0)

            # Equipment information (1 point)
            if customer.equipment:
                score += 1.0

            session.close()
            return min(score / max_score, 1.0)

        except Exception as e:
            logger.error(f"Error calculating data quality score: {e}")
            return 0.0
