#!/usr/bin/env python3
"""
Advanced Email Analysis Pipeline for HVAC CRM
Integrates LM Studio (Gemma3-4b) with MongoDB for intelligent email processing
"""

import os
import json
import requests
import imaplib
import email
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import smtplib
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import pymongo
from pymongo import MongoClient
import psycopg2
import psycopg2.extras
import re
from dataclasses import dataclass, asdict
import base64

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("email_analysis.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
LM_STUDIO_URL = "http://*************:1234"
MONGODB_URL = "******************************************************"
POSTGRES_CONFIG = {
    'host': '**************',
    'port': '5432',
    'database': 'hvacdb',
    'user': 'hvacdb',
    'password': 'blaeritipol'
}

# Email accounts
EMAIL_ACCOUNTS = {
    'grzegorz': {
        'email': '<EMAIL>',
        'password': os.environ.get('GRZEGORZ_EMAIL_PASSWORD', ''),
        'imap_server': 'imap.gmail.com',
        'smtp_server': 'smtp.gmail.com',
        'type': 'customer_emails'
    },
    'dolores': {
        'email': '<EMAIL>', 
        'password': os.environ.get('DOLORES_EMAIL_PASSWORD', ''),
        'imap_server': 'imap.gmail.com',
        'smtp_server': 'smtp.gmail.com',
        'type': 'transcriptions'
    }
}

@dataclass
class EmailAnalysis:
    """Email analysis result structure"""
    email_id: str
    sender: str
    subject: str
    content: str
    timestamp: datetime
    analysis_type: str
    
    # AI Analysis Results
    intent: str
    sentiment: str
    priority: str
    customer_info: Dict[str, Any]
    equipment_info: Dict[str, Any]
    action_items: List[str]
    
    # HVAC Specific
    service_type: str
    urgency_level: int
    estimated_cost: Optional[float]
    location: str
    
    # Metadata
    processed_at: datetime
    model_used: str
    confidence_score: float

class EmailAnalyzer:
    """Advanced email analyzer using LM Studio and MongoDB"""
    
    def __init__(self):
        self.lm_studio_url = LM_STUDIO_URL
        self.mongo_client = MongoClient(MONGODB_URL)
        self.mongo_db = self.mongo_client['hvac_email_analysis']
        self.postgres_conn = None
        self._connect_postgres()
        
    def _connect_postgres(self):
        """Connect to PostgreSQL database"""
        try:
            self.postgres_conn = psycopg2.connect(**POSTGRES_CONFIG)
            logger.info("✅ Connected to PostgreSQL database")
        except Exception as e:
            logger.error(f"❌ Failed to connect to PostgreSQL: {e}")
            
    def test_lm_studio_connection(self) -> bool:
        """Test connection to LM Studio"""
        try:
            response = requests.get(f"{self.lm_studio_url}/v1/models", timeout=10)
            if response.status_code == 200:
                models = response.json()
                logger.info(f"✅ LM Studio connected. Available models: {models}")
                return True
            else:
                logger.error(f"❌ LM Studio connection failed: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ LM Studio connection error: {e}")
            return False
            
    def analyze_with_gemma3(self, email_content: str, email_type: str) -> Dict[str, Any]:
        """Analyze email content using Gemma3-4b via LM Studio"""
        
        # Create specialized prompt based on email type
        if email_type == 'customer_emails':
            prompt = f"""
Analyze this HVAC customer email and extract key information:

EMAIL CONTENT:
{email_content}

Please provide a JSON response with the following structure:
{{
    "intent": "service_request|quote_request|complaint|inquiry|other",
    "sentiment": "positive|neutral|negative",
    "priority": "low|medium|high|urgent",
    "customer_info": {{
        "name": "extracted customer name",
        "phone": "extracted phone number",
        "address": "extracted address",
        "company": "company name if business"
    }},
    "equipment_info": {{
        "type": "klimatyzacja|pompa_ciepla|rekuperator|other",
        "brand": "equipment brand if mentioned",
        "model": "equipment model if mentioned",
        "problem": "described problem or need"
    }},
    "service_type": "installation|repair|maintenance|consultation",
    "urgency_level": 1-5,
    "estimated_cost": null,
    "location": "extracted location/city",
    "action_items": ["list of required actions"],
    "confidence_score": 0.0-1.0
}}

Focus on HVAC-specific terminology and Polish language context.
"""
        else:  # transcriptions
            prompt = f"""
Analyze this HVAC service transcription and extract key information:

TRANSCRIPTION CONTENT:
{email_content}

Please provide a JSON response with the following structure:
{{
    "intent": "service_report|customer_call|technical_discussion|other",
    "sentiment": "positive|neutral|negative", 
    "priority": "low|medium|high|urgent",
    "customer_info": {{
        "name": "customer name from transcription",
        "phone": "phone number if mentioned",
        "address": "service address",
        "company": "company name if mentioned"
    }},
    "equipment_info": {{
        "type": "klimatyzacja|pompa_ciepla|rekuperator|other",
        "brand": "equipment brand",
        "model": "equipment model",
        "problem": "technical issue described"
    }},
    "service_type": "installation|repair|maintenance|consultation",
    "urgency_level": 1-5,
    "estimated_cost": null,
    "location": "service location",
    "action_items": ["follow-up actions needed"],
    "confidence_score": 0.0-1.0
}}

Focus on technical HVAC details and service outcomes.
"""

        try:
            response = requests.post(
                f"{self.lm_studio_url}/v1/chat/completions",
                json={
                    "model": "gemma-2-2b-it",  # Adjust model name as needed
                    "messages": [
                        {"role": "system", "content": "You are an expert HVAC business analyst. Analyze emails and transcriptions to extract structured business intelligence."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.3,
                    "max_tokens": 1000
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # Try to extract JSON from the response
                try:
                    # Look for JSON in the response
                    json_start = content.find('{')
                    json_end = content.rfind('}') + 1
                    if json_start != -1 and json_end != -1:
                        json_str = content[json_start:json_end]
                        analysis = json.loads(json_str)
                        analysis['model_used'] = 'gemma3-4b'
                        return analysis
                except json.JSONDecodeError:
                    logger.warning("Failed to parse JSON from LM Studio response")
                    
                # Fallback: create basic analysis
                return {
                    "intent": "unknown",
                    "sentiment": "neutral", 
                    "priority": "medium",
                    "customer_info": {},
                    "equipment_info": {},
                    "service_type": "unknown",
                    "urgency_level": 3,
                    "estimated_cost": None,
                    "location": "",
                    "action_items": [],
                    "confidence_score": 0.5,
                    "model_used": "gemma3-4b",
                    "raw_response": content
                }
            else:
                logger.error(f"LM Studio API error: {response.status_code}")
                return self._create_fallback_analysis()
                
        except Exception as e:
            logger.error(f"Error analyzing with Gemma3: {e}")
            return self._create_fallback_analysis()
            
    def _create_fallback_analysis(self) -> Dict[str, Any]:
        """Create fallback analysis when AI fails"""
        return {
            "intent": "unknown",
            "sentiment": "neutral",
            "priority": "medium", 
            "customer_info": {},
            "equipment_info": {},
            "service_type": "unknown",
            "urgency_level": 3,
            "estimated_cost": None,
            "location": "",
            "action_items": [],
            "confidence_score": 0.1,
            "model_used": "fallback"
        }
        
    def fetch_emails(self, account_name: str, days_back: int = 7) -> List[Dict[str, Any]]:
        """Fetch emails from specified account"""
        account = EMAIL_ACCOUNTS.get(account_name)
        if not account:
            logger.error(f"Unknown email account: {account_name}")
            return []
            
        if not account['password']:
            logger.warning(f"No password configured for {account_name}")
            return []
            
        try:
            # Connect to IMAP server
            mail = imaplib.IMAP4_SSL(account['imap_server'])
            mail.login(account['email'], account['password'])
            mail.select('inbox')
            
            # Search for emails from last N days
            since_date = (datetime.now() - timedelta(days=days_back)).strftime("%d-%b-%Y")
            result, data = mail.search(None, f'(SINCE "{since_date}")')
            
            emails = []
            email_ids = data[0].split()
            
            logger.info(f"Found {len(email_ids)} emails in {account_name} from last {days_back} days")
            
            for email_id in email_ids[-50:]:  # Process last 50 emails
                result, data = mail.fetch(email_id, '(RFC822)')
                raw_email = data[0][1]
                email_message = email.message_from_bytes(raw_email)
                
                # Extract email content
                content = self._extract_email_content(email_message)
                
                emails.append({
                    'id': email_id.decode(),
                    'sender': email_message.get('From', ''),
                    'subject': email_message.get('Subject', ''),
                    'date': email_message.get('Date', ''),
                    'content': content,
                    'account_type': account['type']
                })
                
            mail.close()
            mail.logout()
            
            return emails
            
        except Exception as e:
            logger.error(f"Error fetching emails from {account_name}: {e}")
            return []
            
    def _extract_email_content(self, email_message) -> str:
        """Extract text content from email message"""
        content = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                if part.get_content_type() == "text/plain":
                    content += part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            content = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
            
        return content.strip()
        
    def save_analysis_to_mongodb(self, analysis: EmailAnalysis):
        """Save email analysis to MongoDB"""
        try:
            collection = self.mongo_db['email_analyses']
            doc = asdict(analysis)
            
            # Convert datetime objects to strings for MongoDB
            doc['timestamp'] = doc['timestamp'].isoformat()
            doc['processed_at'] = doc['processed_at'].isoformat()
            
            result = collection.insert_one(doc)
            logger.info(f"✅ Saved analysis to MongoDB: {result.inserted_id}")
            return result.inserted_id
            
        except Exception as e:
            logger.error(f"❌ Error saving to MongoDB: {e}")
            return None
            
    def save_analysis_to_postgres(self, analysis: EmailAnalysis):
        """Save email analysis to PostgreSQL"""
        try:
            with self.postgres_conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO enhanced_email_analysis 
                    (email_id, sender, subject, content, timestamp, analysis_type,
                     intent, sentiment, priority, customer_info, equipment_info, 
                     action_items, service_type, urgency_level, estimated_cost, 
                     location, processed_at, model_used, confidence_score)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    analysis.email_id, analysis.sender, analysis.subject, 
                    analysis.content, analysis.timestamp, analysis.analysis_type,
                    analysis.intent, analysis.sentiment, analysis.priority,
                    json.dumps(analysis.customer_info), json.dumps(analysis.equipment_info),
                    json.dumps(analysis.action_items), analysis.service_type,
                    analysis.urgency_level, analysis.estimated_cost, analysis.location,
                    analysis.processed_at, analysis.model_used, analysis.confidence_score
                ))
            self.postgres_conn.commit()
            logger.info("✅ Saved analysis to PostgreSQL")
            
        except Exception as e:
            logger.error(f"❌ Error saving to PostgreSQL: {e}")
            self.postgres_conn.rollback()
            
    def process_emails(self, account_name: str, days_back: int = 7):
        """Main email processing pipeline"""
        logger.info(f"🚀 Starting email analysis for {account_name}")
        
        # Test LM Studio connection
        if not self.test_lm_studio_connection():
            logger.error("Cannot proceed without LM Studio connection")
            return
            
        # Fetch emails
        emails = self.fetch_emails(account_name, days_back)
        if not emails:
            logger.warning(f"No emails found for {account_name}")
            return
            
        logger.info(f"📧 Processing {len(emails)} emails...")
        
        for email_data in emails:
            try:
                # Analyze with Gemma3-4b
                ai_analysis = self.analyze_with_gemma3(
                    email_data['content'], 
                    email_data['account_type']
                )
                
                # Create EmailAnalysis object
                analysis = EmailAnalysis(
                    email_id=email_data['id'],
                    sender=email_data['sender'],
                    subject=email_data['subject'],
                    content=email_data['content'],
                    timestamp=datetime.now(),  # Parse from email_data['date'] if needed
                    analysis_type=email_data['account_type'],
                    
                    intent=ai_analysis.get('intent', 'unknown'),
                    sentiment=ai_analysis.get('sentiment', 'neutral'),
                    priority=ai_analysis.get('priority', 'medium'),
                    customer_info=ai_analysis.get('customer_info', {}),
                    equipment_info=ai_analysis.get('equipment_info', {}),
                    action_items=ai_analysis.get('action_items', []),
                    
                    service_type=ai_analysis.get('service_type', 'unknown'),
                    urgency_level=ai_analysis.get('urgency_level', 3),
                    estimated_cost=ai_analysis.get('estimated_cost'),
                    location=ai_analysis.get('location', ''),
                    
                    processed_at=datetime.now(),
                    model_used=ai_analysis.get('model_used', 'gemma3-4b'),
                    confidence_score=ai_analysis.get('confidence_score', 0.5)
                )
                
                # Save to both databases
                self.save_analysis_to_mongodb(analysis)
                self.save_analysis_to_postgres(analysis)
                
                logger.info(f"✅ Processed email: {email_data['subject'][:50]}...")
                
            except Exception as e:
                logger.error(f"❌ Error processing email {email_data['id']}: {e}")
                continue
                
        logger.info(f"🎉 Completed email analysis for {account_name}")

def main():
    """Main function to run email analysis"""
    logger.info("🚀 Starting Advanced Email Analysis Pipeline")
    
    analyzer = EmailAnalyzer()
    
    # Process both email accounts
    for account_name in ['grzegorz', 'dolores']:
        try:
            analyzer.process_emails(account_name, days_back=30)
        except Exception as e:
            logger.error(f"Error processing {account_name}: {e}")
            continue
    
    logger.info("✅ Email analysis pipeline completed")

if __name__ == "__main__":
    main()